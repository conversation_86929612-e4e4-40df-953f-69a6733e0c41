#!/usr/bin/env node
/**
 * 序列号哈希解码器 - 基于sequenceNumber + base64JSON的哈希分析
 */

const crypto = require('crypto');

class SequenceHashDecoder {
    constructor() {
        this.targetHash = '4C77729A443981236D4BEBD58C7A3B1222EA5DCE';
    }
    
    /**
     * 生成可能的LOGIN JSON对象
     */
    generateLoginObjects() {
        const objects = [];
        
        // 基础的LOGIN对象模板
        const baseTemplate = {
            sequenceNumber: 0,  // 会被动态设置
            tutorialMode: false,
            ticket: null,
            observerMode: false,
            observerTarget: -1,
            loginCode: "",      // 空字符串
            loginHash: "",      // 空字符串
            powerUser: false,
            ownAnyPrivateServer: false,
            featureComb: null,
            serverId: "server1"
        };
        
        // 变体1：loginCode和loginHash都为空字符串
        objects.push({
            ...baseTemplate,
            loginCode: "",
            loginHash: ""
        });
        
        // 变体2：loginCode和loginHash都为null
        objects.push({
            ...baseTemplate,
            loginCode: null,
            loginHash: null
        });
        
        // 变体3：只有loginCode为空
        objects.push({
            ...baseTemplate,
            loginCode: "",
            loginHash: "some_hash_value"
        });
        
        // 变体4：只有loginHash为空
        objects.push({
            ...baseTemplate,
            loginCode: "some_code_value",
            loginHash: ""
        });
        
        // 变体5：包含一些常见的默认值
        objects.push({
            ...baseTemplate,
            loginCode: "0",
            loginHash: "0"
        });
        
        // 变体6：tutorialMode为true
        objects.push({
            ...baseTemplate,
            tutorialMode: true,
            loginCode: "",
            loginHash: ""
        });
        
        return objects;
    }
    
    /**
     * 测试特定的sequenceNumber和JSON对象组合
     */
    testCombination(sequenceNumber, loginObject, description = '') {
        // 设置sequenceNumber
        loginObject.sequenceNumber = sequenceNumber;
        
        // 转换为JSON字符串
        const jsonString = JSON.stringify(loginObject);
        
        // UTF-8编码
        const utf8Bytes = Buffer.from(jsonString, 'utf8');
        
        // Base64编码
        const base64String = utf8Bytes.toString('base64');
        
        // 拼接sequenceNumber字符串和base64字符串
        const finalString = sequenceNumber.toString() + base64String;
        
        // 生成哈希
        const algorithms = ['sha1', 'md5', 'sha256'];
        
        console.log(`\n🔍 测试: ${description || `序列号${sequenceNumber}`}`);
        console.log(`   序列号: ${sequenceNumber}`);
        console.log(`   JSON: ${jsonString}`);
        console.log(`   Base64: ${base64String}`);
        console.log(`   最终字符串: ${finalString}`);
        console.log(`   最终字符串长度: ${finalString.length}`);
        
        let found = false;
        algorithms.forEach(alg => {
            try {
                const hash = crypto.createHash(alg).update(finalString).digest('hex');
                const match = hash.toUpperCase() === this.targetHash.toUpperCase();
                
                console.log(`   ${alg.toUpperCase()}: ${hash} ${match ? '✅ 匹配!' : ''}`);
                
                if (match) {
                    console.log(`\n🎉 找到匹配！`);
                    console.log(`   算法: ${alg.toUpperCase()}`);
                    console.log(`   序列号: ${sequenceNumber}`);
                    console.log(`   输入字符串: "${finalString}"`);
                    console.log(`   JSON对象:`, loginObject);
                    found = true;
                }
            } catch (e) {
                console.log(`   ${alg.toUpperCase()}: 错误 - ${e.message}`);
            }
        });
        
        return found;
    }
    
    /**
     * 测试不同的字符串拼接方式
     */
    testDifferentConcatenations(sequenceNumber, base64String) {
        const combinations = [
            { value: sequenceNumber.toString() + base64String, desc: `序列号 + Base64` },
            { value: base64String + sequenceNumber.toString(), desc: `Base64 + 序列号` },
            { value: `${sequenceNumber}_${base64String}`, desc: `序列号_Base64` },
            { value: `${sequenceNumber}:${base64String}`, desc: `序列号:Base64` },
            { value: `${sequenceNumber}|${base64String}`, desc: `序列号|Base64` },
            { value: `LOGIN${sequenceNumber}${base64String}`, desc: `LOGIN + 序列号 + Base64` },
            { value: `${sequenceNumber}${base64String}#`, desc: `序列号 + Base64 + #` }
        ];
        
        console.log(`\n🔗 测试不同的拼接方式:`);
        
        let found = false;
        combinations.forEach(combo => {
            console.log(`\n   ${combo.desc}: "${combo.value}"`);
            
            const algorithms = ['sha1', 'md5', 'sha256'];
            algorithms.forEach(alg => {
                try {
                    const hash = crypto.createHash(alg).update(combo.value).digest('hex');
                    const match = hash.toUpperCase() === this.targetHash.toUpperCase();
                    
                    console.log(`     ${alg.toUpperCase()}: ${hash} ${match ? '✅ 匹配!' : ''}`);
                    
                    if (match) {
                        console.log(`\n🎉 找到匹配！`);
                        console.log(`   拼接方式: ${combo.desc}`);
                        console.log(`   输入字符串: "${combo.value}"`);
                        found = true;
                    }
                } catch (e) {
                    console.log(`     ${alg.toUpperCase()}: 错误 - ${e.message}`);
                }
            });
        });
        
        return found;
    }
    
    /**
     * 主要的解码分析
     */
    async decode() {
        console.log(`🎯 目标哈希: ${this.targetHash}`);
        console.log(`📏 哈希长度: ${this.targetHash.length} 字符 (SHA-1)`);
        console.log(`🚀 开始序列号哈希解码分析...\n`);
        
        const loginObjects = this.generateLoginObjects();
        console.log(`📊 生成了 ${loginObjects.length} 个LOGIN对象变体`);
        
        // 测试不同的序列号（1-100）
        let found = false;
        
        for (let seq = 1; seq <= 100 && !found; seq++) {
            for (let i = 0; i < loginObjects.length && !found; i++) {
                const obj = loginObjects[i];
                const description = `序列号${seq}, 变体${i + 1}`;
                
                found = this.testCombination(seq, JSON.parse(JSON.stringify(obj)), description);
                
                if (!found) {
                    // 如果基础组合没找到，测试不同的拼接方式
                    obj.sequenceNumber = seq;
                    const jsonString = JSON.stringify(obj);
                    const base64String = Buffer.from(jsonString, 'utf8').toString('base64');
                    
                    found = this.testDifferentConcatenations(seq, base64String);
                }
            }
            
            // 显示进度
            if (seq % 10 === 0) {
                console.log(`⏳ 进度: 已测试序列号 1-${seq}`);
            }
        }
        
        if (!found) {
            console.log(`\n❌ 未找到匹配`);
            console.log(`💡 可能的原因:`);
            console.log(`   1. JSON对象的结构与实际不同`);
            console.log(`   2. 使用了不同的字符串拼接方式`);
            console.log(`   3. 包含了额外的盐值或前缀`);
            console.log(`   4. 序列号超出了测试范围（1-100）`);
            
            // 提供一些调试信息
            this.provideDebuggingInfo();
        }
        
        return found;
    }
    
    /**
     * 提供调试信息
     */
    provideDebuggingInfo() {
        console.log(`\n🔧 调试信息:`);
        
        // 生成一个示例来展示格式
        const exampleObj = {
            sequenceNumber: 1,
            tutorialMode: false,
            ticket: null,
            observerMode: false,
            observerTarget: -1,
            loginCode: "",
            loginHash: "",
            powerUser: false,
            ownAnyPrivateServer: false,
            featureComb: null,
            serverId: "server1"
        };
        
        const jsonString = JSON.stringify(exampleObj);
        const base64String = Buffer.from(jsonString, 'utf8').toString('base64');
        const finalString = "1" + base64String;
        
        console.log(`   示例JSON: ${jsonString}`);
        console.log(`   示例Base64: ${base64String}`);
        console.log(`   示例最终字符串: ${finalString}`);
        console.log(`   示例SHA1: ${crypto.createHash('sha1').update(finalString).digest('hex')}`);
        
        console.log(`\n🔍 建议手动检查:`);
        console.log(`   1. 确认JSON对象的确切结构`);
        console.log(`   2. 检查是否有特殊的字符编码`);
        console.log(`   3. 验证字符串拼接的顺序`);
        console.log(`   4. 考虑是否有额外的处理步骤`);
    }
}

// 使用示例
async function main() {
    const decoder = new SequenceHashDecoder();
    await decoder.decode();
}

if (require.main === module) {
    main().catch(console.error);
}
