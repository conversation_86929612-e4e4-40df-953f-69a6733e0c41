import base64

def decode_base64_string(s):
    try:
        # 添加填充
        padding = 4 - len(s) % 4
        if padding != 4:
            s += '=' * padding
        decoded = base64.b64decode(s)

        # 尝试多种编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'utf-16', 'latin1']
        for encoding in encodings:
            try:
                result = decoded.decode(encoding)
                print(f"成功使用 {encoding} 编码: {result}")
                return result
            except:
                continue
        return "所有编码都失败"
    except Exception as e:
        return f"Base64解码失败: {e}"

# 解码诅咒消息
curse_message = "biM5pyb5L2g6KKr6amx6YCQLCDml6DIkI0h"
result = decode_base64_string(curse_message)
print(f"Base64字符串: {curse_message}")
print(f"解码结果: {result}")

# 显示字节内容
try:
    padding = 4 - len(curse_message) % 4
    if padding != 4:
        curse_message += '=' * padding
    decoded_bytes = base64.b64decode(curse_message)
    print(f"字节内容: {decoded_bytes}")
    print(f"十六进制: {decoded_bytes.hex()}")

    # 尝试将字节按UTF-8三字节序列解析
    print("\n尝试UTF-8三字节序列解析:")
    i = 0
    while i < len(decoded_bytes):
        if i + 2 < len(decoded_bytes):
            # 尝试三字节UTF-8字符
            try:
                char_bytes = decoded_bytes[i:i+3]
                char = char_bytes.decode('utf-8')
                print(f"字节 {char_bytes.hex()}: {char}")
                i += 3
            except:
                i += 1
        else:
            i += 1

except Exception as e:
    print(f"字节解码失败: {e}")

# 测试一些可能的中文消息
print("\n=== 测试中文消息编码 ===")
test_messages = [
    "你被驱逐了",
    "违规行为",
    "破坏游戏",
    "恶意玩家",
    "griefing",
    "cheating",
    "bad player"
]

for msg in test_messages:
    try:
        utf8_bytes = msg.encode('utf-8')
        base64_str = base64.b64encode(utf8_bytes).decode('ascii')
        print(f"'{msg}' -> {base64_str}")
        if base64_str == curse_message:
            print(f"  *** 匹配！原始消息是: {msg}")
    except Exception as e:
        print(f"编码 '{msg}' 失败: {e}")

# 尝试将字节序列当作可能的中文字符
print("\n=== 尝试解析为可能的中文 ===")
hex_str = "6e2339a726f92f683a28aafa6a6c7a60240b0839a5e832242348"
bytes_data = bytes.fromhex(hex_str)

# 尝试不同的分割方式
print("原始字节:", bytes_data)
print("尝试UTF-8解码（忽略错误）:")
try:
    result = bytes_data.decode('utf-8', errors='ignore')
    print(f"忽略错误的结果: '{result}'")
except:
    pass

try:
    result = bytes_data.decode('utf-8', errors='replace')
    print(f"替换错误的结果: '{result}'")
except:
    pass

# 也许这是某种游戏内部的编码或者是特定的诅咒理由ID
print(f"\n可能的含义:")
print(f"1. 这可能是游戏内预设的诅咒理由编号或标识符")
print(f"2. 这可能是损坏的UTF-8编码的中文文本")
print(f"3. 这可能是游戏内部使用的特殊编码格式")
