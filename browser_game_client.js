/**
 * 浏览器游戏客户端 - 可在浏览器控制台中运行
 * 使用WebSocket或者通过代理服务器与游戏服务器通信
 */

class BrowserGameClient {
    constructor(serverUrl = 'ws://localhost:8080') {
        this.serverUrl = serverUrl;
        this.socket = null;
        this.connected = false;
        this.messageCallback = null;
        this.connectionCallback = null;
        
        // 统计信息
        this.sentCommands = 0;
        this.receivedMessages = 0;
        this.startTime = Date.now();
        
        // 命令队列
        this.commandQueue = [];
        this.isProcessingQueue = false;
    }
    
    /**
     * 连接到游戏服务器
     */
    connect() {
        return new Promise((resolve, reject) => {
            try {
                this.socket = new WebSocket(this.serverUrl);
                
                this.socket.onopen = () => {
                    this.connected = true;
                    this.startTime = Date.now();
                    console.log(`✅ 成功连接到服务器 ${this.serverUrl}`);
                    
                    if (this.connectionCallback) {
                        this.connectionCallback(true);
                    }
                    resolve(true);
                };
                
                this.socket.onmessage = (event) => {
                    this.handleReceivedMessage(event.data);
                };
                
                this.socket.onclose = () => {
                    this.connected = false;
                    console.log('🔌 连接已关闭');
                    this.showStats();
                };
                
                this.socket.onerror = (error) => {
                    console.error('❌ WebSocket错误:', error);
                    if (this.connectionCallback) {
                        this.connectionCallback(false);
                    }
                    reject(error);
                };
                
            } catch (error) {
                console.error('❌ 连接失败:', error);
                reject(error);
            }
        });
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.connected = false;
    }
    
    /**
     * 处理接收到的消息
     */
    handleReceivedMessage(message) {
        this.receivedMessages++;
        console.log(`📥 [${this.receivedMessages}] ${message}`);
        
        if (this.messageCallback) {
            this.messageCallback(message);
        }
    }
    
    /**
     * 发送原始命令
     */
    sendRawCommand(command) {
        if (!this.connected || !this.socket) {
            console.error('❌ 未连接到服务器');
            return false;
        }
        
        try {
            this.socket.send(command);
            this.sentCommands++;
            console.log(`📤 [${this.sentCommands}] ${command}`);
            return true;
        } catch (error) {
            console.error(`❌ 发送失败: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 添加命令到队列
     */
    queueCommand(command, delay = 0) {
        this.commandQueue.push({ command, delay, timestamp: Date.now() });
        this.processCommandQueue();
    }
    
    /**
     * 处理命令队列
     */
    async processCommandQueue() {
        if (this.isProcessingQueue || this.commandQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.commandQueue.length > 0) {
            const { command, delay } = this.commandQueue.shift();
            
            if (delay > 0) {
                await this.sleep(delay);
            }
            
            this.sendRawCommand(command);
        }
        
        this.isProcessingQueue = false;
    }
    
    /**
     * 显示统计信息
     */
    showStats() {
        const runtime = (Date.now() - this.startTime) / 1000;
        console.log('📊 运行统计:');
        console.log(`   运行时间: ${runtime.toFixed(1)}秒`);
        console.log(`   发送命令: ${this.sentCommands}`);
        console.log(`   接收消息: ${this.receivedMessages}`);
    }
    
    // ==================== 游戏命令方法 ====================
    
    /**
     * 登录命令
     */
    login(sequenceNumber = 1, loginCode = '', loginHash = '', delay = 0) {
        const command = `LOGIN ${sequenceNumber} false null false -1 ${loginCode} ${loginHash} false false null server1`;
        this.queueCommand(command, delay);
    }
    
    /**
     * 移动玩家
     */
    movePlayer(x, y, direction = 0, delay = 0) {
        const command = `MOVE ${x} ${y} ${direction}`;
        this.queueCommand(command, delay);
    }
    
    /**
     * 发送聊天消息
     */
    sayMessage(message, delay = 0) {
        const command = `SAY ${message}`;
        this.queueCommand(command, delay);
    }
    
    /**
     * 使用物品
     */
    useObject(x, y, objectId, delay = 0) {
        const command = `USE ${x} ${y} ${objectId}`;
        this.queueCommand(command, delay);
    }
    
    /**
     * 掉落物品
     */
    dropItem(x, y, slotIndex, delay = 0) {
        const command = `DROP ${x} ${y} ${slotIndex}`;
        this.queueCommand(command, delay);
    }
    
    /**
     * 诅咒玩家
     */
    cursePlayer(playerId, reason, x, y, cause = 1, isChild = false, delay = 0) {
        // Base64编码诅咒理由
        const encodedReason = btoa(unescape(encodeURIComponent(reason)));
        const command = `CURSEPLAYER ${playerId} ${encodedReason} ${x} ${y} ${cause} ${isChild ? 1 : 0}`;
        this.queueCommand(command, delay);
    }
    
    /**
     * 杀死玩家
     */
    killPlayer(x, y, playerId = null, delay = 0) {
        let command;
        if (playerId !== null) {
            command = `KILL ${x} ${y} ${playerId}`;
        } else {
            command = `KILL ${x} ${y}`;
        }
        this.queueCommand(command, delay);
    }
    
    /**
     * 设置家园标记
     */
    setHome(x, y, flag = 1, delay = 0) {
        const command = `HOME ${x} ${y} ${flag}`;
        this.queueCommand(command, delay);
    }
    
    // ==================== 自动化功能 ====================
    
    /**
     * 自动诅咒玩家多次
     */
    autoCursePlayer(playerId, reason, x, y, count = 6, interval = 1000) {
        console.log(`🎯 开始自动诅咒玩家 ${playerId}，共 ${count} 次，间隔 ${interval}ms`);
        
        for (let i = 0; i < count; i++) {
            this.cursePlayer(playerId, `${reason} #${i + 1}`, x, y, 1, false, i * interval);
        }
        
        console.log('✅ 自动诅咒命令已加入队列');
    }
    
    /**
     * 自动移动路径
     */
    autoMovePattern(positions, interval = 1000) {
        console.log(`🚶 开始自动移动，共 ${positions.length} 个位置`);
        
        for (let i = 0; i < positions.length; i++) {
            const [x, y] = positions[i];
            this.movePlayer(x, y, 0, i * interval);
        }
        
        console.log('✅ 自动移动命令已加入队列');
    }
    
    /**
     * 刷屏聊天
     */
    spamChat(message, count = 10, interval = 500) {
        console.log(`💬 开始刷屏聊天，共 ${count} 条消息`);
        
        for (let i = 0; i < count; i++) {
            this.sayMessage(`${message} #${i + 1}`, i * interval);
        }
        
        console.log('✅ 刷屏聊天命令已加入队列');
    }
    
    /**
     * 批量操作 - 连续使用物品
     */
    batchUseItems(x, y, objectIds, interval = 200) {
        console.log(`🔧 开始批量使用物品，共 ${objectIds.length} 个物品`);
        
        for (let i = 0; i < objectIds.length; i++) {
            this.useObject(x, y, objectIds[i], i * interval);
        }
        
        console.log('✅ 批量使用物品命令已加入队列');
    }
    
    /**
     * 清空命令队列
     */
    clearQueue() {
        this.commandQueue = [];
        console.log('🗑️ 命令队列已清空');
    }
    
    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置消息回调
     */
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }
    
    /**
     * 设置连接回调
     */
    setConnectionCallback(callback) {
        this.connectionCallback = callback;
    }
}

// ==================== 浏览器控制台使用示例 ====================

/**
 * 在浏览器控制台中使用的示例代码
 * 复制以下代码到浏览器控制台中运行
 */

/*
// 创建客户端实例
const gameClient = new BrowserGameClient('ws://your_websocket_proxy:8080');

// 设置消息处理
gameClient.setMessageCallback((message) => {
    if (message.includes('CURSE')) {
        console.log('🔥 检测到诅咒相关消息!');
    } else if (message.includes('KILL')) {
        console.log('⚔️ 检测到杀死相关消息!');
    }
});

// 连接并开始操作
gameClient.connect().then(() => {
    // 登录
    gameClient.login(1, 'your_code', 'your_hash');
    
    // 发送聊天消息
    gameClient.sayMessage('Hello from browser client!', 1000);
    
    // 自动移动
    const positions = [[100, 100], [200, 200], [300, 300]];
    gameClient.autoMovePattern(positions, 1500);
    
    // 批量使用物品
    const itemIds = [123, 456, 789];
    gameClient.batchUseItems(100, 200, itemIds, 500);
    
    // 危险操作示例（谨慎使用！）
    // gameClient.autoCursePlayer(566030, '测试诅咒', 100, 200, 3, 2000);
    
}).catch(console.error);

// 其他有用的命令：
// gameClient.showStats();           // 显示统计信息
// gameClient.clearQueue();          // 清空命令队列
// gameClient.disconnect();          // 断开连接
*/

// 导出类（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BrowserGameClient;
}

// 全局暴露（在浏览器中使用）
if (typeof window !== 'undefined') {
    window.BrowserGameClient = BrowserGameClient;
}
