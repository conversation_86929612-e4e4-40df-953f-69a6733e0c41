#!/usr/bin/env node
/**
 * 高级自动化游戏客户端
 * 包含复杂的自动化策略和批量操作功能
 */

const GameServerClient = require('./game_client.js');

class AdvancedAutomationClient extends GameServerClient {
    constructor(serverHost, serverPort) {
        super(serverHost, serverPort);
        
        // 自动化配置
        this.automationConfig = {
            enabled: false,
            strategies: new Map(),
            currentStrategy: null,
            loopInterval: 1000
        };
        
        // 玩家数据缓存
        this.playerCache = new Map();
        this.gameState = {
            selfPosition: { x: 0, y: 0 },
            nearbyPlayers: [],
            inventory: [],
            curseTokens: 0
        };
    }
    
    /**
     * 启动自动化系统
     */
    startAutomation(strategyName) {
        if (!this.automationConfig.strategies.has(strategyName)) {
            console.error(`❌ 未找到策略: ${strategyName}`);
            return false;
        }
        
        this.automationConfig.enabled = true;
        this.automationConfig.currentStrategy = strategyName;
        
        console.log(`🤖 启动自动化策略: ${strategyName}`);
        this.runAutomationLoop();
        return true;
    }
    
    /**
     * 停止自动化
     */
    stopAutomation() {
        this.automationConfig.enabled = false;
        this.automationConfig.currentStrategy = null;
        console.log('🛑 自动化已停止');
    }
    
    /**
     * 自动化主循环
     */
    async runAutomationLoop() {
        while (this.automationConfig.enabled && this.connected) {
            try {
                const strategy = this.automationConfig.strategies.get(this.automationConfig.currentStrategy);
                if (strategy) {
                    await strategy.execute(this);
                }
                
                await this.sleep(this.automationConfig.loopInterval);
            } catch (error) {
                console.error('❌ 自动化执行错误:', error);
                await this.sleep(5000); // 错误后等待5秒
            }
        }
    }
    
    /**
     * 注册自动化策略
     */
    registerStrategy(name, strategy) {
        this.automationConfig.strategies.set(name, strategy);
        console.log(`📝 注册策略: ${name}`);
    }
    
    // ==================== 高级自动化策略 ====================
    
    /**
     * 批量诅咒策略
     */
    createMassCurseStrategy(targetPlayers, reason, interval = 2000) {
        return {
            name: 'mass_curse',
            targetPlayers,
            reason,
            interval,
            currentIndex: 0,
            lastExecute: 0,
            
            async execute(client) {
                const now = Date.now();
                if (now - this.lastExecute < this.interval) {
                    return;
                }
                
                if (this.currentIndex >= this.targetPlayers.length) {
                    console.log('✅ 批量诅咒完成');
                    client.stopAutomation();
                    return;
                }
                
                const target = this.targetPlayers[this.currentIndex];
                console.log(`🎯 诅咒目标 ${this.currentIndex + 1}/${this.targetPlayers.length}: ${target.id}`);
                
                client.cursePlayer(target.id, this.reason, target.x, target.y);
                
                this.currentIndex++;
                this.lastExecute = now;
            }
        };
    }
    
    /**
     * 自动巡逻策略
     */
    createPatrolStrategy(waypoints, speed = 1000) {
        return {
            name: 'patrol',
            waypoints,
            speed,
            currentWaypoint: 0,
            lastMove: 0,
            
            async execute(client) {
                const now = Date.now();
                if (now - this.lastMove < this.speed) {
                    return;
                }
                
                const waypoint = this.waypoints[this.currentWaypoint];
                console.log(`🚶 巡逻到点 ${this.currentWaypoint + 1}/${this.waypoints.length}: (${waypoint.x}, ${waypoint.y})`);
                
                client.movePlayer(waypoint.x, waypoint.y);
                
                this.currentWaypoint = (this.currentWaypoint + 1) % this.waypoints.length;
                this.lastMove = now;
            }
        };
    }
    
    /**
     * 自动收集资源策略
     */
    createResourceGatherStrategy(resourceIds, searchRadius = 10) {
        return {
            name: 'resource_gather',
            resourceIds,
            searchRadius,
            lastScan: 0,
            scanInterval: 5000,
            
            async execute(client) {
                const now = Date.now();
                if (now - this.lastScan < this.scanInterval) {
                    return;
                }
                
                // 模拟扫描周围资源
                const { x, y } = client.gameState.selfPosition;
                
                for (const resourceId of this.resourceIds) {
                    // 在搜索半径内寻找资源
                    for (let dx = -this.searchRadius; dx <= this.searchRadius; dx++) {
                        for (let dy = -this.searchRadius; dy <= this.searchRadius; dy++) {
                            const targetX = x + dx;
                            const targetY = y + dy;
                            
                            // 尝试使用资源
                            client.useObject(targetX, targetY, resourceId);
                            await client.sleep(100); // 短暂延迟
                        }
                    }
                }
                
                this.lastScan = now;
                console.log('🔍 完成一轮资源扫描');
            }
        };
    }
    
    /**
     * 防御策略 - 自动反击
     */
    createDefenseStrategy(retaliate = true) {
        return {
            name: 'defense',
            retaliate,
            threatList: new Set(),
            
            async execute(client) {
                // 监控威胁并自动反击
                // 这里需要根据接收到的消息来判断威胁
                
                if (this.retaliate && this.threatList.size > 0) {
                    for (const threatId of this.threatList) {
                        console.log(`⚔️ 反击威胁目标: ${threatId}`);
                        client.cursePlayer(threatId, '自卫反击', 0, 0);
                    }
                    this.threatList.clear();
                }
            }
        };
    }
    
    // ==================== 批量操作功能 ====================
    
    /**
     * 批量诅咒多个玩家
     */
    async batchCurseMultiplePlayers(targets, reason, interval = 2000) {
        console.log(`🎯 开始批量诅咒 ${targets.length} 个玩家`);
        
        for (let i = 0; i < targets.length; i++) {
            const target = targets[i];
            console.log(`诅咒进度: ${i + 1}/${targets.length} - 玩家ID: ${target.id}`);
            
            this.cursePlayer(target.id, `${reason} (${i + 1}/${targets.length})`, target.x, target.y);
            
            if (i < targets.length - 1) {
                await this.sleep(interval);
            }
        }
        
        console.log('✅ 批量诅咒完成');
    }
    
    /**
     * 区域清理 - 杀死指定区域内的所有玩家
     */
    async clearArea(centerX, centerY, radius, reason = '区域清理') {
        console.log(`🧹 开始清理区域 (${centerX}, ${centerY}) 半径: ${radius}`);
        
        // 生成区域内的所有坐标点
        const targets = [];
        for (let x = centerX - radius; x <= centerX + radius; x++) {
            for (let y = centerY - radius; y <= centerY + radius; y++) {
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                if (distance <= radius) {
                    targets.push({ x, y });
                }
            }
        }
        
        console.log(`🎯 区域内共有 ${targets.length} 个目标点`);
        
        for (let i = 0; i < targets.length; i++) {
            const { x, y } = targets[i];
            this.killPlayer(x, y);
            
            if (i % 10 === 0) {
                console.log(`清理进度: ${i + 1}/${targets.length}`);
                await this.sleep(100); // 每10个目标暂停一下
            }
        }
        
        console.log('✅ 区域清理完成');
    }
    
    /**
     * 刷屏攻击
     */
    async spamAttack(messages, interval = 100, duration = 30000) {
        console.log(`💥 开始刷屏攻击，持续 ${duration / 1000} 秒`);
        
        const startTime = Date.now();
        let messageIndex = 0;
        
        while (Date.now() - startTime < duration) {
            const message = messages[messageIndex % messages.length];
            this.sayMessage(`${message} [${Date.now()}]`);
            
            messageIndex++;
            await this.sleep(interval);
        }
        
        console.log(`✅ 刷屏攻击完成，共发送 ${messageIndex} 条消息`);
    }
    
    // ==================== 消息解析和状态更新 ====================
    
    /**
     * 重写消息处理，添加状态解析
     */
    handleReceivedMessage(message) {
        super.handleReceivedMessage(message);
        
        // 解析游戏状态
        this.parseGameState(message);
        
        // 检测威胁
        this.detectThreats(message);
    }
    
    /**
     * 解析游戏状态
     */
    parseGameState(message) {
        // 解析位置信息
        if (message.includes('MOVE')) {
            // 更新位置信息
        }
        
        // 解析玩家信息
        if (message.includes('PLAYER_UPDATE')) {
            // 更新玩家缓存
        }
        
        // 解析诅咒令牌
        if (message.includes('CURSE_TOKEN')) {
            // 更新诅咒令牌数量
        }
    }
    
    /**
     * 检测威胁
     */
    detectThreats(message) {
        // 检测针对自己的诅咒
        if (message.includes('CURSEPLAYER') && message.includes('your_player_id')) {
            const playerId = this.extractPlayerIdFromMessage(message);
            if (playerId) {
                const defense = this.automationConfig.strategies.get('defense');
                if (defense) {
                    defense.threatList.add(playerId);
                    console.log(`⚠️ 检测到威胁玩家: ${playerId}`);
                }
            }
        }
    }
    
    /**
     * 从消息中提取玩家ID
     */
    extractPlayerIdFromMessage(message) {
        const match = message.match(/CURSEPLAYER (\d+)/);
        return match ? parseInt(match[1]) : null;
    }
}

// ==================== 使用示例 ====================

async function advancedDemo() {
    const client = new AdvancedAutomationClient('your_server_ip', 8005);
    
    // 注册各种自动化策略
    const patrolPoints = [
        { x: 100, y: 100 },
        { x: 200, y: 200 },
        { x: 300, y: 300 },
        { x: 200, y: 100 }
    ];
    client.registerStrategy('patrol', client.createPatrolStrategy(patrolPoints, 2000));
    
    const resourceIds = [123, 456, 789]; // 要收集的资源ID
    client.registerStrategy('gather', client.createResourceGatherStrategy(resourceIds, 5));
    
    client.registerStrategy('defense', client.createDefenseStrategy(true));
    
    try {
        await client.connect();
        
        // 登录
        client.login(1, 'your_code', 'your_hash');
        await client.sleep(2000);
        
        // 启动巡逻策略
        client.startAutomation('patrol');
        
        // 运行30秒后切换到收集策略
        setTimeout(() => {
            client.stopAutomation();
            client.startAutomation('gather');
        }, 30000);
        
        // 保持运行
        console.log('🤖 高级自动化客户端运行中...');
        
    } catch (error) {
        console.error('❌ 启动失败:', error);
    }
}

// 导出类
module.exports = AdvancedAutomationClient;

// 如果直接运行
if (require.main === module) {
    advancedDemo().catch(console.error);
}
