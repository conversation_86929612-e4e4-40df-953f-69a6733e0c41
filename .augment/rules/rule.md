---
type: "always_apply"
---

# 通用用户规则文档

## 基础代码调整原则

1. **全局思维优先**：采用系统性思维分析问题，统筹考虑所有相关组件和依赖关系，避免局部修改引发连锁问题。

2. **架构理解先行**：在进行任何修改前，先全面了解项目结构、模块关系和功能分布，确保不出现功能重复或冲突。

3. **问题溯源分析**：深入排查问题根源及其影响范围，制定全面的解决方案，避免头痛医头、脚痛医脚的做法。

4. **规范一致性**：严格遵循项目开发规范和编码标准，确保修改内容与整体架构保持一致。

5. **依赖关系检查**：修改后必须验证所有相关的引用、导入和依赖关系是否正确。

6. **文件创建权限**：创建新文件需要明确说明必要性和用途，获得确认后方可执行。

7. **质量复查机制**：完成修改后进行全面的代码审查和功能验证。

8. **重复代码避免**：在实现新功能前，先检查是否存在类似实现，避免代码重复。

9. **冗余清理**：修改完成后清理所有冗余代码、无用导入和重复逻辑。

10. **路径规范化**：统一使用相对路径，确保项目的可移植性。

## 开发协作与修改规范

### 1. 系统性思维与影响评估

**全局视角**：每次修改前从系统整体角度分析潜在影响，确保不破坏现有功能或引入新问题。

**影响评估**：全面评估修改对系统各层面的影响，包括数据流、接口契约、状态管理等关键环节。

**协调性原则**：确保修改与其他模块协调工作，避免"修复一个问题却引入另一个问题"的情况。

### 2. 标准化修改流程
**问题定位与排查**：
- 明确问题：准确定位问题根源，明确具体表现和影响范围
- 排查方法：运用日志分析、调试工具、测试用例等手段系统性排查
- 根因分析：深入分析问题本质，确保解决方案治本而非治标

**修改方案设计**：
- 最小化原则：设计最小化修改方案，避免不必要的代码变动
- 可维护性：保持代码高可读性和可维护性，遵循既定的编码规范
- 兼容性保证：确保修改与现有功能完全兼容，不引入新的兼容性问题

**测试与验证**：
- 单元测试：确保相关单元测试通过，验证修改的正确性
- 集成测试：验证修改在集成环境中的正常运行
- 回归测试：确保修改未引入新问题或导致功能退化
### 3. 权限控制与代码保护

**权限边界**：未经明确授权，不得修改与当前问题无关的代码，严格保护现有功能的完整性。

**代码安全**：修改过程中防止意外删除或覆盖有效代码，建立适当的备份和回滚机制。

### 4. 效率与质量平衡

**开发效率**：运用自动化工具、代码生成、模板化开发等手段提升效率，减少重复劳动。

**质量标准**：确保修改后的代码符合质量标准，遵循最佳实践，保持简洁、清晰和可维护性。

**文档同步**：及时更新相关文档，包括接口文档、代码注释、说明文档等，确保信息同步。

### 5. 跨层协作规范

**API接口一致性**：确保各层间接口的输入输出格式保持一致，避免因接口变更导致功能失效。

**数据流一致性**：应确保前后端的数据流保持一致，避免因数据格式或状态管理不一致导致的问题。

**状态管理**：确保修改不会影响其他组件的状态，保持状态的一致性和可预测性。

### 6. 审查与反馈机制

**代码审查**：每次修改后提供详细的审查报告，说明修改内容、原因及潜在影响。

**反馈循环**：建立有效的反馈机制，确保修改符合需求和预期。

### 7. 集成与部署规范

**持续集成**：确保每次代码修改都能通过自动化集成流程，保证代码质量和稳定性。

**自动化部署**：支持自动化部署流程，确保代码能够快速、安全地部署，减少人为错误。

## 工作原则与沟通规范

### 核心工作原则

**系统性思维**：始终以全局视角进行代码修改，确保每次修改都能与系统其他部分协调工作。

**最小化干预**：确保每次修改都是最小化的，避免对无关代码进行不必要的改动。

**效率与质量并重**：通过自动化工具和最佳实践，在提高开发效率的同时保持代码的高质量和可维护性。

### 任务执行规范

**任务边界**：专注于指定任务，不自行扩展范围，除非明确要求进行全局性考虑。

### 开发效率与代码质量补充规范

**记忆能力**：关键产品规则和逻辑自动存入记忆，并在记忆中标记所属项目避免记忆混乱，在Trae中使用的是Memory MCP。

**上下文**：当你陷入循环时，请综合文档集里的内容，找出问题核心并告知用户。在用户介入后，开启深度调试模式来解决问题。

**代码质量**：应确保每次修改后的代码符合项目的代码质量标准，调用context7 MCP来提升代码质量。

**忽略文件**：不要忽略.gitignore文件，确保忽略文件的内容正确。不要忽略环境变量数据库配置文件，即使.gitignore文件中忽略.env和.env.production和my.cnf文件，你也不要去忽略它们。

**工具使用**：不要在聊天窗口运行终端，自动放到窗口去。合理使用开发工具，避免在不适当的环境中执行操作。

**沟通语言**：全程使用中文进行交流，确保沟通的准确性和效率。
