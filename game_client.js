#!/usr/bin/env node
/**
 * 独立游戏服务器客户端 - JavaScript版本
 * 完全绕过游戏客户端，直接与服务器通信
 * 
 * 使用方法:
 * npm install ws
 * node game_client.js
 */

const net = require('net');
const crypto = require('crypto');

class GameServerClient {
    constructor(serverHost = 'localhost', serverPort = 8005) {
        this.host = serverHost;
        this.port = serverPort;
        this.socket = null;
        this.connected = false;
        this.messageCallback = null;
        this.connectionCallback = null;
        
        // 统计信息
        this.sentCommands = 0;
        this.receivedMessages = 0;
        this.startTime = Date.now();
        
        // 消息缓冲区
        this.buffer = '';
    }
    
    /**
     * 连接到游戏服务器
     */
    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            
            this.socket.connect(this.port, this.host, () => {
                this.connected = true;
                this.startTime = Date.now();
                console.log(`✅ 成功连接到服务器 ${this.host}:${this.port}`);
                
                if (this.connectionCallback) {
                    this.connectionCallback(true);
                }
                resolve(true);
            });
            
            this.socket.on('data', (data) => {
                this.handleReceivedData(data);
            });
            
            this.socket.on('close', () => {
                this.connected = false;
                console.log('🔌 连接已关闭');
                this.showStats();
            });
            
            this.socket.on('error', (err) => {
                console.error(`❌ 连接错误: ${err.message}`);
                if (this.connectionCallback) {
                    this.connectionCallback(false);
                }
                reject(err);
            });
        });
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
        }
        this.connected = false;
    }
    
    /**
     * 处理接收到的数据
     */
    handleReceivedData(data) {
        this.buffer += data.toString();
        
        // 处理完整的消息行
        while (this.buffer.includes('\n')) {
            const lineEnd = this.buffer.indexOf('\n');
            const message = this.buffer.substring(0, lineEnd);
            this.buffer = this.buffer.substring(lineEnd + 1);
            
            if (message.trim()) {
                this.handleReceivedMessage(message.trim());
            }
        }
    }
    
    /**
     * 处理接收到的消息
     */
    handleReceivedMessage(message) {
        this.receivedMessages++;
        console.log(`📥 [${this.receivedMessages}] ${message}`);
        
        if (this.messageCallback) {
            this.messageCallback(message);
        }
    }
    
    /**
     * 发送原始命令
     */
    sendRawCommand(command) {
        if (!this.connected || !this.socket) {
            console.error('❌ 未连接到服务器');
            return false;
        }
        
        try {
            const message = command + '\n';
            this.socket.write(message);
            this.sentCommands++;
            console.log(`📤 [${this.sentCommands}] ${command}`);
            return true;
        } catch (error) {
            console.error(`❌ 发送失败: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 显示统计信息
     */
    showStats() {
        const runtime = (Date.now() - this.startTime) / 1000;
        console.log('📊 运行统计:');
        console.log(`   运行时间: ${runtime.toFixed(1)}秒`);
        console.log(`   发送命令: ${this.sentCommands}`);
        console.log(`   接收消息: ${this.receivedMessages}`);
    }
    
    // ==================== 游戏命令方法 ====================
    
    /**
     * 登录命令
     */
    login(sequenceNumber = 1, loginCode = '', loginHash = '') {
        const command = `LOGIN ${sequenceNumber} false null false -1 ${loginCode} ${loginHash} false false null server1`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 移动玩家
     */
    movePlayer(x, y, direction = 0) {
        const command = `MOVE ${x} ${y} ${direction}`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 发送聊天消息
     */
    sayMessage(message) {
        const command = `SAY ${message}`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 使用物品
     */
    useObject(x, y, objectId) {
        const command = `USE ${x} ${y} ${objectId}`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 掉落物品
     */
    dropItem(x, y, slotIndex) {
        const command = `DROP ${x} ${y} ${slotIndex}`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 诅咒玩家
     */
    cursePlayer(playerId, reason, x, y, cause = 1, isChild = false) {
        // Base64编码诅咒理由
        const encodedReason = Buffer.from(reason, 'utf8').toString('base64');
        const command = `CURSEPLAYER ${playerId} ${encodedReason} ${x} ${y} ${cause} ${isChild ? 1 : 0}`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 杀死玩家
     */
    killPlayer(x, y, playerId = null) {
        let command;
        if (playerId !== null) {
            command = `KILL ${x} ${y} ${playerId}`;
        } else {
            command = `KILL ${x} ${y}`;
        }
        return this.sendRawCommand(command);
    }
    
    /**
     * 设置家园标记
     */
    setHome(x, y, flag = 1) {
        const command = `HOME ${x} ${y} ${flag}`;
        return this.sendRawCommand(command);
    }
    
    /**
     * 着陆命令
     */
    land() {
        const command = 'LAND';
        return this.sendRawCommand(command);
    }
    
    // ==================== 自动化功能 ====================
    
    /**
     * 自动诅咒玩家多次
     */
    async autoCursePlayer(playerId, reason, x, y, count = 6, interval = 1000) {
        console.log(`🎯 开始自动诅咒玩家 ${playerId}，共 ${count} 次，间隔 ${interval}ms`);
        
        for (let i = 0; i < count; i++) {
            this.cursePlayer(playerId, `${reason} #${i + 1}`, x, y);
            
            if (i < count - 1) {
                await this.sleep(interval);
            }
        }
        
        console.log('✅ 自动诅咒完成');
    }
    
    /**
     * 自动移动路径
     */
    async autoMovePattern(positions, interval = 1000) {
        console.log(`🚶 开始自动移动，共 ${positions.length} 个位置`);
        
        for (let i = 0; i < positions.length; i++) {
            const [x, y] = positions[i];
            this.movePlayer(x, y);
            
            if (i < positions.length - 1) {
                await this.sleep(interval);
            }
        }
        
        console.log('✅ 自动移动完成');
    }
    
    /**
     * 刷屏聊天
     */
    async spamChat(message, count = 10, interval = 500) {
        console.log(`💬 开始刷屏聊天，共 ${count} 条消息`);
        
        for (let i = 0; i < count; i++) {
            this.sayMessage(`${message} #${i + 1}`);
            
            if (i < count - 1) {
                await this.sleep(interval);
            }
        }
        
        console.log('✅ 刷屏聊天完成');
    }
    
    /**
     * 批量操作 - 连续使用物品
     */
    async batchUseItems(x, y, objectIds, interval = 200) {
        console.log(`🔧 开始批量使用物品，共 ${objectIds.length} 个物品`);
        
        for (let i = 0; i < objectIds.length; i++) {
            this.useObject(x, y, objectIds[i]);
            
            if (i < objectIds.length - 1) {
                await this.sleep(interval);
            }
        }
        
        console.log('✅ 批量使用物品完成');
    }
    
    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置消息回调
     */
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }
    
    /**
     * 设置连接回调
     */
    setConnectionCallback(callback) {
        this.connectionCallback = callback;
    }
}

// ==================== 使用示例 ====================

async function main() {
    const client = new GameServerClient('your_server_ip', 8005); // 替换为实际服务器地址
    
    // 设置消息处理回调
    client.setMessageCallback((message) => {
        // 处理特定的服务器消息
        if (message.includes('CURSE')) {
            console.log('🔥 检测到诅咒相关消息!');
        } else if (message.includes('KILL')) {
            console.log('⚔️ 检测到杀死相关消息!');
        } else if (message.includes('MOVE')) {
            console.log('🚶 检测到移动相关消息!');
        }
    });
    
    // 设置连接回调
    client.setConnectionCallback(async (connected) => {
        if (connected) {
            console.log('🎮 连接成功，开始执行自动化任务...');
            
            // 登录
            client.login(1, 'your_login_code', 'your_login_hash');
            await client.sleep(1000);
            
            // 发送聊天消息
            client.sayMessage('Hello from standalone JavaScript client!');
            await client.sleep(1000);
            
            // 自动移动示例
            const positions = [[100, 100], [200, 200], [300, 300], [100, 100]];
            await client.autoMovePattern(positions, 1500);
            
            // 使用物品示例
            client.useObject(100, 200, 123);
            await client.sleep(1000);
            
            // 设置家园标记
            client.setHome(100, 200, 1);
            await client.sleep(1000);
            
            // 注意：以下是危险操作，仅用于测试！
            // await client.autoCursePlayer(566030, '测试诅咒', 100, 200, 3, 2000);
            
        } else {
            console.log('❌ 连接失败');
        }
    });
    
    try {
        await client.connect();
        
        console.log('🎮 JavaScript客户端运行中，按Ctrl+C退出...');
        
        // 保持程序运行
        process.on('SIGINT', () => {
            console.log('\n👋 用户退出');
            client.disconnect();
            process.exit(0);
        });
        
        // 防止程序退出
        setInterval(() => {}, 1000);
        
    } catch (error) {
        console.error('❌ 无法连接到服务器:', error.message);
    }
}

// 如果直接运行此文件，则执行main函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = GameServerClient;
