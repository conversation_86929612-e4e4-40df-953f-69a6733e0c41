# JavaScript游戏客户端使用说明

## 📋 概述

这套JavaScript客户端可以完全绕过游戏客户端，直接与服务器通信。包含三个版本：

1. **game_client.js** - Node.js基础版本
2. **browser_game_client.js** - 浏览器版本
3. **advanced_automation.js** - 高级自动化版本

## 🚀 快速开始

### Node.js版本使用

```bash
# 安装Node.js依赖（如果需要）
npm install

# 运行基础客户端
node game_client.js

# 运行高级自动化客户端
node advanced_automation.js
```

### 浏览器版本使用

1. 打开浏览器开发者工具（F12）
2. 复制`browser_game_client.js`的内容到控制台
3. 创建客户端实例并连接：

```javascript
const client = new BrowserGameClient('ws://your_proxy_server:8080');
client.connect().then(() => {
    client.login(1, 'your_code', 'your_hash');
    client.sayMessage('Hello from browser!');
});
```

## 🎮 基本命令

### 连接和登录
```javascript
// 连接到服务器
await client.connect();

// 登录
client.login(sequenceNumber, loginCode, loginHash);
```

### 移动和交互
```javascript
// 移动玩家
client.movePlayer(x, y, direction);

// 发送聊天消息
client.sayMessage("Hello World!");

// 使用物品
client.useObject(x, y, objectId);

// 掉落物品
client.dropItem(x, y, slotIndex);

// 设置家园标记
client.setHome(x, y, flag);
```

### 高级操作
```javascript
// 诅咒玩家
client.cursePlayer(playerId, reason, x, y, cause, isChild);

// 杀死玩家
client.killPlayer(x, y, playerId);
```

## 🤖 自动化功能

### 批量操作
```javascript
// 自动诅咒玩家多次
await client.autoCursePlayer(playerId, "违规行为", x, y, 6, 2000);

// 自动移动路径
const positions = [[100, 100], [200, 200], [300, 300]];
await client.autoMovePattern(positions, 1500);

// 刷屏聊天
await client.spamChat("测试消息", 10, 500);

// 批量使用物品
const itemIds = [123, 456, 789];
await client.batchUseItems(x, y, itemIds, 200);
```

### 高级自动化策略
```javascript
const advancedClient = new AdvancedAutomationClient('server_ip', 8005);

// 巡逻策略
const waypoints = [
    { x: 100, y: 100 },
    { x: 200, y: 200 },
    { x: 300, y: 300 }
];
advancedClient.registerStrategy('patrol', 
    advancedClient.createPatrolStrategy(waypoints, 2000));

// 启动自动化
advancedClient.startAutomation('patrol');
```

## 📡 网络协议

### 命令格式
所有命令都是文本格式，以换行符结尾：

```
LOGIN 1 false null false -1 code hash false false null server1
MOVE 100 200 0
SAY Hello World
USE 100 200 123
DROP 100 200 1
CURSEPLAYER 566030 dGVzdA== 100 200 1 0
KILL 100 200 566030
HOME 100 200 1
LAND
```

### Base64编码
某些参数（如诅咒理由）需要Base64编码：

```javascript
// JavaScript中的Base64编码
const encoded = btoa(unescape(encodeURIComponent("中文文本")));

// Node.js中的Base64编码
const encoded = Buffer.from("中文文本", 'utf8').toString('base64');
```

## ⚠️ 安全注意事项

### 服务器验证
- 服务器会验证命令的合法性
- 异常频率的操作可能被检测
- 某些操作需要满足游戏规则（距离、权限等）

### 使用建议
1. **测试环境**：先在测试服务器上验证功能
2. **合理频率**：避免过于频繁的操作
3. **遵守规则**：不要违反游戏服务条款
4. **备份账号**：在非主要账号上测试

### 风险控制
```javascript
// 添加延迟避免被检测
await client.sleep(1000);

// 随机化操作间隔
const randomDelay = Math.random() * 1000 + 500;
await client.sleep(randomDelay);

// 监控服务器响应
client.setMessageCallback((message) => {
    if (message.includes('ERROR') || message.includes('BANNED')) {
        console.log('⚠️ 检测到异常响应，停止操作');
        client.disconnect();
    }
});
```

## 🔧 故障排除

### 连接问题
```javascript
// 检查连接状态
if (!client.connected) {
    console.log('未连接到服务器');
    await client.connect();
}

// 设置连接超时
client.socket.setTimeout(10000);
```

### 消息处理
```javascript
// 调试消息
client.setMessageCallback((message) => {
    console.log('收到消息:', message);
    
    // 解析特定消息类型
    if (message.startsWith('ERROR')) {
        console.error('服务器错误:', message);
    }
});
```

### 命令队列
```javascript
// 清空命令队列（浏览器版本）
client.clearQueue();

// 检查队列状态
console.log('队列长度:', client.commandQueue.length);
```

## 📊 统计和监控

### 运行统计
```javascript
// 显示统计信息
client.showStats();

// 输出示例：
// 📊 运行统计:
//    运行时间: 120.5秒
//    发送命令: 45
//    接收消息: 23
```

### 性能监控
```javascript
// 监控发送频率
setInterval(() => {
    const rate = client.sentCommands / ((Date.now() - client.startTime) / 1000);
    console.log(`发送频率: ${rate.toFixed(2)} 命令/秒`);
}, 10000);
```

## 🎯 实际应用示例

### 示例1：自动收集资源
```javascript
async function autoGather() {
    const client = new GameServerClient('server_ip', 8005);
    await client.connect();
    
    client.login(1, 'code', 'hash');
    await client.sleep(2000);
    
    // 在指定区域收集资源
    for (let x = 50; x <= 150; x += 10) {
        for (let y = 50; y <= 150; y += 10) {
            client.useObject(x, y, 123); // 资源ID 123
            await client.sleep(200);
        }
    }
}
```

### 示例2：自动防御
```javascript
async function autoDefense() {
    const client = new GameServerClient('server_ip', 8005);
    
    client.setMessageCallback((message) => {
        // 检测到针对自己的诅咒时自动反击
        if (message.includes('CURSEPLAYER') && message.includes('your_id')) {
            const attackerId = extractAttackerId(message);
            client.cursePlayer(attackerId, '自卫反击', 0, 0);
        }
    });
    
    await client.connect();
    client.login(1, 'code', 'hash');
}
```

### 示例3：批量管理
```javascript
async function batchManagement() {
    const client = new AdvancedAutomationClient('server_ip', 8005);
    await client.connect();
    
    // 批量诅咒违规玩家
    const violators = [
        { id: 123, x: 100, y: 200 },
        { id: 456, x: 150, y: 250 },
        { id: 789, x: 200, y: 300 }
    ];
    
    await client.batchCurseMultiplePlayers(violators, '违规行为', 3000);
}
```

## 📝 开发和扩展

### 添加新命令
```javascript
// 在客户端类中添加新方法
customCommand(param1, param2) {
    const command = `CUSTOM ${param1} ${param2}`;
    return this.sendRawCommand(command);
}
```

### 创建自定义策略
```javascript
// 创建自定义自动化策略
const customStrategy = {
    name: 'custom',
    async execute(client) {
        // 自定义逻辑
        console.log('执行自定义策略');
    }
};

client.registerStrategy('custom', customStrategy);
```

这套JavaScript客户端提供了完整的游戏服务器通信功能，可以根据需要进行定制和扩展。记住要负责任地使用这些工具！
