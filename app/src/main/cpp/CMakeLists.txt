cmake_minimum_required(VERSION 3.18.1)

project("core")

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
#set(CMAKE_CXX_VISIBILITY_PRESET "hidden")
#set(CMAKE_C_VISIBILITY_PRESET "hidden")

FILE(GLOB_RECURSE FILE_INCLUDES include/*.h*)
include_directories(
        include/
        include/imgui/
        include/xdl/
        include/gameHack/
        ${ANDROID_NDK}/sources/android/native_app_glue
)

add_library(${CMAKE_PROJECT_NAME} SHARED
        source/Tools.cpp
        source/Il2Cpp.cpp
        source/fake_dlfcn.cpp
        source/imgui/imgui.cpp
        source/imgui/imgui_draw.cpp
        source/imgui/imgui_demo.cpp
        source/imgui/imgui_tables.cpp
        source/imgui/imgui_widgets.cpp
        source/imgui/imgui_impl_android.cpp
        source/imgui/imgui_impl_opengl3.cpp
        source/xdl/xdl.c
        source/xdl/xdl_iterate.c
        source/xdl/xdl_linker.c
        source/xdl/xdl_lzma.c
        source/xdl/xdl_util.c
        main.cpp
        #${ANDROID_NDK}/sources/android/native_app_glue/android_native_app_glue.c
)

find_library(
        log-lib
        log)

target_link_libraries(
        ${CMAKE_PROJECT_NAME}
        android
        EGL
        GLESv3
        log
        jnigraphics
        ${CMAKE_SOURCE_DIR}/lib/${ANDROID_ABI}/libdobby.a
#        ${CMAKE_SOURCE_DIR}/lib/${ANDROID_ABI}/libluajit.a
        ${log-lib})
