#pragma once

#define _USE_MATH_DEFINES
#include <math.h>

struct Vector2Int
{
    union
    {
        struct
        {
            int X;
            int Y;
        };
        int data[2];
    };


    /**
     * Constructors.
     */
    inline Vector2Int();
    inline Vector2Int(int data[]);
    inline Vector2Int(int value);
    inline Vector2Int(int x, int y);


    /**
     * Constants for common vectors.
     */
    static inline Vector2Int Zero();
    static inline Vector2Int One();
    static inline Vector2Int Right();
    static inline Vector2Int Left();
    static inline Vector2Int Up();
    static inline Vector2Int Down();


    /**
     * Returns the distance between a and b.
     * @param a: The first point.
     * @param b: The second point.
     * @return: A scalar value.
     */
    static inline float Distance(Vector2Int a, Vector2Int b);

    /**
     * Returns the dot product of two vectors.
     * @param lhs: The left side of the multiplication.
     * @param rhs: The right side of the multiplication.
     * @return: A scalar value.
     */
    static inline int Dot(Vector2Int lhs, Vector2Int rhs);

    /**
     * Returns a vector linearly interpolated between a and b, moving along
     * a straight line. The vector is clamped to never go beyond the end points.
     * @param a: The starting point.
     * @param b: The ending point.
     * @param t: The interpolation value [0-1].
     * @return: A new vector.
     */
    static inline Vector2Int Lerp(Vector2Int a, Vector2Int b, float t);

    /**
     * Returns the magnitude of a vector.
     * @param v: The vector in question.
     * @return: A scalar value.
     */
    static inline float Magnitude(Vector2Int v);

    /**
     * Returns a vector made from the largest components of two other vectors.
     * @param a: The first vector.
     * @param b: The second vector.
     * @return: A new vector.
     */
    static inline Vector2Int Max(Vector2Int a, Vector2Int b);

    /**
     * Returns a vector made from the smallest components of two other vectors.
     * @param a: The first vector.
     * @param b: The second vector.
     * @return: A new vector.
     */
    static inline Vector2Int Min(Vector2Int a, Vector2Int b);

    /**
     * Multiplies two vectors component-wise.
     * @param a: The lhs of the multiplication.
     * @param b: The rhs of the multiplication.
     * @return: A new vector.
     */
    static inline Vector2Int Scale(Vector2Int a, Vector2Int b);

    /**
     * Returns the squared magnitude of a vector.
     * This is useful when comparing relative lengths, where the exact length
     * is not important, and much time can be saved by not calculating the
     * square root.
     * @param v: The vector in question.
     * @return: A scalar value.
     */
    static inline int SqrMagnitude(Vector2Int v);


    /**
     * Operator overloading.
     */
    inline struct Vector2Int& operator+=(const int rhs);
    inline struct Vector2Int& operator-=(const int rhs);
    inline struct Vector2Int& operator*=(const int rhs);
    inline struct Vector2Int& operator/=(const int rhs);
    inline struct Vector2Int& operator+=(const Vector2Int rhs);
    inline struct Vector2Int& operator-=(const Vector2Int rhs);
};

inline Vector2Int operator-(Vector2Int rhs);
inline Vector2Int operator+(Vector2Int lhs, const int rhs);
inline Vector2Int operator-(Vector2Int lhs, const int rhs);
inline Vector2Int operator*(Vector2Int lhs, const int rhs);
inline Vector2Int operator/(Vector2Int lhs, const int rhs);
inline Vector2Int operator+(const int lhs, Vector2Int rhs);
inline Vector2Int operator-(const int lhs, Vector2Int rhs);
inline Vector2Int operator*(const int lhs, Vector2Int rhs);
inline Vector2Int operator/(const int lhs, Vector2Int rhs);
inline Vector2Int operator+(Vector2Int lhs, const Vector2Int rhs);
inline Vector2Int operator-(Vector2Int lhs, const Vector2Int rhs);
inline bool operator==(const Vector2Int lhs, const Vector2Int rhs);
inline bool operator!=(const Vector2Int lhs, const Vector2Int rhs);



/*******************************************************************************
 * Implementation
 */

Vector2Int::Vector2Int() : X(0), Y(0) {}
Vector2Int::Vector2Int(int data[]) : X(data[0]), Y(data[1]) {}
Vector2Int::Vector2Int(int value) : X(value), Y(value) {}
Vector2Int::Vector2Int(int x, int y) : X(x), Y(y) {}


Vector2Int Vector2Int::Zero() { return Vector2Int(0, 0); }
Vector2Int Vector2Int::One() { return Vector2Int(1, 1); }
Vector2Int Vector2Int::Right() { return Vector2Int(1, 0); }
Vector2Int Vector2Int::Left() { return Vector2Int(-1, 0); }
Vector2Int Vector2Int::Up() { return Vector2Int(0, 1); }
Vector2Int Vector2Int::Down() { return Vector2Int(0, -1); }


float Vector2Int::Distance(Vector2Int a, Vector2Int b)
{
    return Vector2Int::Magnitude(a - b);
}

int Vector2Int::Dot(Vector2Int lhs, Vector2Int rhs)
{
    return lhs.X * rhs.X + lhs.Y * rhs.Y;
}

Vector2Int Vector2Int::Lerp(Vector2Int a, Vector2Int b, float t)
{
    if (t < 0) return a;
    else if (t > 1) return b;
    return Vector2Int((int)((b.X - a.X) * t + a.X), (int)((b.Y - a.Y) * t + a.Y));
}

float Vector2Int::Magnitude(Vector2Int v)
{
    return sqrt((float)SqrMagnitude(v));
}

Vector2Int Vector2Int::Max(Vector2Int a, Vector2Int b)
{
    int x = a.X > b.X ? a.X : b.X;
    int y = a.Y > b.Y ? a.Y : b.Y;
    return Vector2Int(x, y);
}

Vector2Int Vector2Int::Min(Vector2Int a, Vector2Int b)
{
    int x = a.X > b.X ? b.X : a.X;
    int y = a.Y > b.Y ? b.Y : a.Y;
    return Vector2Int(x, y);
}

Vector2Int Vector2Int::Scale(Vector2Int a, Vector2Int b)
{
    return Vector2Int(a.X * b.X, a.Y * b.Y);
}

int Vector2Int::SqrMagnitude(Vector2Int v)
{
    return v.X * v.X + v.Y * v.Y;
}


struct Vector2Int& Vector2Int::operator+=(const int rhs)
{
    X += rhs;
    Y += rhs;
    return *this;
}

struct Vector2Int& Vector2Int::operator-=(const int rhs)
{
    X -= rhs;
    Y -= rhs;
    return *this;
}

struct Vector2Int& Vector2Int::operator*=(const int rhs)
{
    X *= rhs;
    Y *= rhs;
    return *this;
}

struct Vector2Int& Vector2Int::operator/=(const int rhs)
{
    X /= rhs;
    Y /= rhs;
    return *this;
}

struct Vector2Int& Vector2Int::operator+=(const Vector2Int rhs)
{
    X += rhs.X;
    Y += rhs.Y;
    return *this;
}

struct Vector2Int& Vector2Int::operator-=(const Vector2Int rhs)
{
    X -= rhs.X;
    Y -= rhs.Y;
    return *this;
}

Vector2Int operator-(Vector2Int rhs) { return rhs * -1; }
Vector2Int operator+(Vector2Int lhs, const int rhs) { return lhs += rhs; }
Vector2Int operator-(Vector2Int lhs, const int rhs) { return lhs -= rhs; }
Vector2Int operator*(Vector2Int lhs, const int rhs) { return lhs *= rhs; }
Vector2Int operator/(Vector2Int lhs, const int rhs) { return lhs /= rhs; }
Vector2Int operator+(const int lhs, Vector2Int rhs) { return rhs += lhs; }
Vector2Int operator-(const int lhs, Vector2Int rhs) { return rhs -= lhs; }
Vector2Int operator*(const int lhs, Vector2Int rhs) { return rhs *= lhs; }
Vector2Int operator/(const int lhs, Vector2Int rhs) { return rhs /= lhs; }
Vector2Int operator+(Vector2Int lhs, const Vector2Int rhs) { return lhs += rhs; }
Vector2Int operator-(Vector2Int lhs, const Vector2Int rhs) { return lhs -= rhs; }

bool operator==(const Vector2Int lhs, const Vector2Int rhs)
{
    return lhs.X == rhs.X && lhs.Y == rhs.Y;
}

bool operator!=(const Vector2Int lhs, const Vector2Int rhs)
{
    return !(lhs == rhs);
}