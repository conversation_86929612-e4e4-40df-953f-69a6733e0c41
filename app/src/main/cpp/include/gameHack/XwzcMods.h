//
// Created by 33735 on 2025/06/24.
//

#pragma once

// --- 前向声明 ---
// 提前告知编译器这些变量的存在，以解决头文件循环依赖问题。
namespace XwzcMods {
    extern bool g_show_virtual_keyboard;
    extern char* g_keyboard_target_buffer;
    extern size_t g_keyboard_target_buffer_size;
    extern const char* g_keyboard_title;
}


#include "imgui.h"
#include "xwzcMods/ObserverHack.h"
#include "xwzcMods/PlayerListHack.h"
#include "xwzcMods/PlayerSelfHack.h"
#include "xwzcMods/DrawHack.h"
#include "xdl.h"
#include "Il2Cpp.h"
#include "dobby.h"
#include "UnityResolve.hpp"
#include <string>
#include <thread>
#include <chrono>
#include <functional>

namespace XwzcMods {

    // 全局初始化状态
    bool g_isInitializing = false;
    bool g_mainInitialized = false;
    std::string g_initStatusMessage = "等待初始化...";

    // --- 主线程任务队列 ---
    std::mutex g_main_thread_tasks_mutex;
    std::vector<std::function<void()>> g_main_thread_tasks;

    /**
     * @brief 向主线程任务队列中添加一个新任务。
     *        这是一个线程安全的函数，可以从任何地方调用。
     * @param task 要在主线程执行的任务函数。
     */
    void AddTaskToMainThread(const std::function<void()>& task) {
        std::lock_guard<std::mutex> lock(g_main_thread_tasks_mutex);
        g_main_thread_tasks.push_back(task);
    }

    // --- 中央虚拟键盘 ---
    bool g_show_virtual_keyboard = false;
    char* g_keyboard_target_buffer = nullptr;
    size_t g_keyboard_target_buffer_size = 0;
    const char* g_keyboard_title = "键盘";

    void ShowVirtualKeyboard() {
        if (!g_show_virtual_keyboard || !g_keyboard_target_buffer) return;

        ImGui::SetNextWindowSize(ImVec2(300, 400), ImGuiCond_FirstUseEver);
        ImGui::Begin(g_keyboard_title, &g_show_virtual_keyboard, ImGuiWindowFlags_NoCollapse);

        // 显示当前输入的文本
        ImGui::TextWrapped("Current: %s", g_keyboard_target_buffer);
        ImGui::Separator();
        
        // 数字键盘布局
        float button_width = ImGui::GetContentRegionAvail().x / 3.0f - ImGui::GetStyle().ItemSpacing.x;
        float button_height = button_width * 0.75f;

        for (int i = 1; i <= 9; ++i) {
            if ((i - 1) % 3 != 0) ImGui::SameLine();
            if (ImGui::Button(std::to_string(i).c_str(), ImVec2(button_width, button_height))) {
                size_t len = strlen(g_keyboard_target_buffer);
                if (len < g_keyboard_target_buffer_size - 1) {
                    g_keyboard_target_buffer[len] = (char)('0' + i);
                    g_keyboard_target_buffer[len + 1] = '\0';
                }
            }
        }

        // 底部按钮
        ImGui::SameLine();
        if (ImGui::Button("0", ImVec2(button_width, button_height))) {
            size_t len = strlen(g_keyboard_target_buffer);
            if (len > 0 && len < g_keyboard_target_buffer_size - 1) { // 只有在非空时才添加0
                g_keyboard_target_buffer[len] = '0';
                g_keyboard_target_buffer[len + 1] = '\0';
            }
        }
        
        if (ImGui::Button("清除", ImVec2(button_width, button_height))) {
            g_keyboard_target_buffer[0] = '\0';
        }
        ImGui::SameLine();
        if (ImGui::Button("退格", ImVec2(button_width, button_height))) {
             size_t len = strlen(g_keyboard_target_buffer);
            if (len > 0) {
                g_keyboard_target_buffer[len - 1] = '\0';
            }
        }
        
        ImGui::End();
    }


    // --- 主线程Hook ---
    void (*old_Update)(void*);
    void Update(void* this_ptr) {
        // --- 1. 执行任务队列 ---
        std::vector<std::function<void()>> tasks_to_run;
        {
            std::lock_guard<std::mutex> lock(g_main_thread_tasks_mutex);
            if (!g_main_thread_tasks.empty()) {
                tasks_to_run.swap(g_main_thread_tasks);
            }
        }
        // 在锁外执行任务，避免持有锁时执行耗时操作
        for (const auto& task : tasks_to_run) {
            task();
        }

        // --- 2. 任务分发 ---
        // 运行所有功能模块
        ObserverHack::OnUpdate();
        PlayerSelfHack::OnUpdate();
        DrawHack::OnUpdate();

        
        // --- 3. 调用原始函数 ---
        if (old_Update) {
            old_Update(this_ptr);
        }
    }


    // 初始化所有功能模块
    void InitAllMods() {
        // 初始化所有功能模块
        ObserverHack::Init();
        PlayerListHack::Init();
        PlayerSelfHack::Init();
        DrawHack::Init();
    }

    void BackgroundLoop() {
        while (true) {
            // 运行所有功能模块
            ObserverHack::Loop();
            PlayerListHack::Loop();
            PlayerSelfHack::Loop();
            DrawHack::Loop();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    // 主初始化函数
    void DoMainInit() {
        g_isInitializing = true;
        
        auto il2cpp_handle = xdl_open("libil2cpp.so", 0);
        if (!il2cpp_handle) {
            g_initStatusMessage = "错误: 无法打开 libil2cpp.so";
            g_isInitializing = false;
            return;
        }
        
        g_initStatusMessage = "正在附加到 Il2Cpp...";
        Il2CppAttach(il2cpp_handle);
        
        g_initStatusMessage = "正在初始化 UnityResolve...";
        UnityResolve::Init(il2cpp_handle, UnityResolve::Mode::Il2Cpp);
        
        g_initStatusMessage = "正在设置主线程Hook...";
        void* navMeshUpdatePtr = Il2CppGetMethodOffset("UnityEngine.AIModule.dll", "UnityEngine.AI", "NavMesh", "Internal_CallOnNavMeshPreUpdate", 0);
        if (navMeshUpdatePtr) {
            DobbyHook(navMeshUpdatePtr, (void*)Update, (void**)&old_Update);
        } else {
            g_initStatusMessage = "错误: 无法找到 NavMesh Update 地址!";
        }

        g_initStatusMessage = "正在初始化功能模块...";
        InitAllMods();

        g_initStatusMessage = "正在启动后台服务...";
        std::thread(BackgroundLoop).detach();
        
        g_initStatusMessage = "初始化完成，一切就绪!";
        g_mainInitialized = true;
        g_isInitializing = false;
    }

    // 绘制主菜单
    void Draw() {
        ImGui::SetNextWindowSize(ImVec2(500, 500), ImGuiCond_FirstUseEver);
        ImGui::Begin("希望之村辅助");

        if (!g_mainInitialized) {
            if (g_isInitializing) {
                ImGui::Text("正在初始化，请稍候...");
            } else {
                if (ImGui::Button("!!! 初始化辅助 !!!")) {
                    std::thread(DoMainInit).detach();
                }
            }
            ImGui::Separator();
            ImGui::Text("状态: %s", g_initStatusMessage.c_str());
            ImGui::TextWrapped("请在进入游戏大厅或稳定场景后，再点击此按钮。");
        } else {
            ImGui::TextColored(ImVec4(0,1,0,1), "辅助已就绪!");
            ImGui::Separator();
            
            // 使用Tab页面布局，避免过多的折叠面板
            if (ImGui::BeginTabBar("MainTabs")) {
                if (ImGui::BeginTabItem("👤 玩家功能")) {
                    // 使用两列布局
                    if (ImGui::BeginTable("PlayerTable", 2, ImGuiTableFlags_Resizable)) {
                        ImGui::TableSetupColumn("玩家自身", ImGuiTableColumnFlags_WidthFixed, 300.0f);
                        ImGui::TableSetupColumn("玩家列表", ImGuiTableColumnFlags_WidthStretch);
                        ImGui::TableHeadersRow();

                        ImGui::TableNextRow();
                        ImGui::TableSetColumnIndex(0);
                        PlayerSelfHack::DrawUI();

                        ImGui::TableSetColumnIndex(1);
                        PlayerListHack::DrawUI();

                        ImGui::EndTable();
                    }
                    ImGui::EndTabItem();
                }
                if (ImGui::BeginTabItem("👁️ 视觉功能")) {
                    // 使用两列布局
                    if (ImGui::BeginTable("VisualTable", 2, ImGuiTableFlags_Resizable)) {
                        ImGui::TableSetupColumn("观察者", ImGuiTableColumnFlags_WidthFixed, 300.0f);
                        ImGui::TableSetupColumn("绘制功能", ImGuiTableColumnFlags_WidthStretch);
                        ImGui::TableHeadersRow();

                        ImGui::TableNextRow();
                        ImGui::TableSetColumnIndex(0);
                        ObserverHack::DrawUI();

                        ImGui::TableSetColumnIndex(1);
                        DrawHack::DrawUI();

                        ImGui::EndTable();
                    }
                    ImGui::EndTabItem();
                }
                ImGui::EndTabBar();
            }
        }

        // 在窗口内容之后，帧结束之前调用Render，这样绘制内容会显示在所有窗口之后（作为背景）
        DrawHack::Render();

        ImGui::End();


        // 在所有窗口之外绘制中央键盘
        ShowVirtualKeyboard();
    }

} // namespace XwzcMods 