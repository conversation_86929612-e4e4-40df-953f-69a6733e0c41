//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include <cstdint>
#include <string>

namespace ObserverHack {

    // 状态定义
    enum class Status {
        NotReady,
        Preparing,
        FieldNotFound,
        Ready
    };

    // 全局变量
    Status g_status = Status::NotReady;
    bool g_observerModeEnabled = false;
    int32_t g_observerFieldOffset = -1;
    int32_t g_targetIdFieldOffset = -1; // 新增的目标ID字段偏移
    int g_observeTargetId = 0;

    // 准备功能的函数
    void Prepare() {
        g_status = Status::Preparing;
        
        // 查找两个字段的偏移
        g_observerFieldOffset = GameHelper::GetFieldOffset("Assembly-CSharp.dll", "", "GameServerManager", "<isObserverMode>k__BackingField");
        g_targetIdFieldOffset = GameHelper::GetFieldOffset("Assembly-CSharp.dll", "", "GameServerManager", "<observeTargetId>k__BackingField");
        
        if (g_observerFieldOffset != -1 && g_targetIdFieldOffset != -1) {
            g_status = Status::Ready;
        } else {
            g_status = Status::FieldNotFound;
        }
    }

    // 绘制UI
    void DrawUI() {
        // 状态显示
        switch (g_status) {
            case Status::NotReady:
            case Status::Preparing:
                ImGui::TextColored(ImVec4(1, 1, 0, 1), "状态: 正在准备功能...");
                break;
            case Status::FieldNotFound:
                ImGui::TextColored(ImVec4(1, 0, 0, 1), "状态: 错误, 字段未找到!");
                break;
            case Status::Ready:
                ImGui::TextColored(ImVec4(0, 1, 0, 1), "状态: 已就绪");
                break;
        }
        ImGui::Separator();

        // 功能开关
        ImGui::Checkbox("启用观察者模式", &g_observerModeEnabled);
        
        // 静态缓冲区，用于与键盘交互
        static char id_buf[16] = "0";

        // 目标ID输入框
        std::string btn_text = "观察目标ID: ";
        g_observeTargetId = atoi(id_buf); // 持续从缓冲区更新
        btn_text += (g_observeTargetId == 0 ? "Tap to enter..." : id_buf);
        
        if (ImGui::Button(btn_text.c_str())) {
        snprintf(id_buf, sizeof(id_buf), "%d", g_observeTargetId);

            // 请求中央键盘
            XwzcMods::g_keyboard_target_buffer = id_buf;
            XwzcMods::g_keyboard_target_buffer_size = sizeof(id_buf);
            XwzcMods::g_keyboard_title = "输入观察目标ID";
            XwzcMods::g_show_virtual_keyboard = true;
        }
    }

    void OnUpdate() {
        // 主线程任务处理入口
    }
    
    // 主循环，由后台线程调用
    void Loop() {
        if (g_status == Status::Ready) {
            void* gameServerManager = GameHelper::getGameServerManager();
            if (gameServerManager) {
                // 写入观察者模式布尔值
                *reinterpret_cast<bool*>(reinterpret_cast<uintptr_t>(gameServerManager) + g_observerFieldOffset) = g_observerModeEnabled;
                
                // 写入观察目标ID整数值
                *reinterpret_cast<int*>(reinterpret_cast<uintptr_t>(gameServerManager) + g_targetIdFieldOffset) = g_observeTargetId;
            }
        }
    }

    // 总初始化，在主初始化之后调用
    void Init() {
        Prepare();
    }
} 