//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include "dobby.h"
#include "Il2Cpp.h"
#include "Unity/Vector2Int.hpp"
#include <cstdint>
#include <string>
#include <vector>
#include <functional>
#include <mutex>
#include <chrono>

// 玩家列表功能模块
namespace PlayerListHack {

    // 功能状态枚举
    enum class Status {
        NotReady,       // 未准备好
        Preparing,      // 正在准备
        FieldNotFound,  // 未找到必要的字段或方法
        Ready           // 功能已就绪
    };

    //================ 全局变量定义 ================//

    Status g_status = Status::NotReady; // 当前功能状态
    void* g_GameUiController = nullptr; // 游戏UI控制器实例

    // 函数指针类型定义
    typedef void (*PlayerListButtonClicked_t)(void*);
    typedef void* (*GetTypeFunc_t)(String*);
    typedef Array<void**>*(*Object_FindObjectsOfType_t)(void*);
    typedef void (*GameUIController_Update_t)(void*);

    // 新增的玩家坐标获取相关函数指针类型
    typedef void* (*PlayerManager_get_allPlayerIds_t)(void*);
    typedef void* (*PlayerManager_FindIPlayer_t)(void*, int32_t);
    typedef Vector2Int (*WorldMap_get_center_t)(void*);

    // 直接定义List<System.Int32>类型
    typedef void* List_Int32_t;

    // 通用IL2CPP调用函数类型
    typedef int32_t (*Il2CppCall_Int32_t)(void*);
    typedef int32_t (*Il2CppCall_Int32_Int32_t)(void*, int32_t);

    // 玩家名称获取相关函数指针类型
    typedef String* (*IPlayerObject_get_displayName_t)(void*);

    // 函数指针实例
    PlayerListButtonClicked_t PlayerListButtonClicked = nullptr; // 玩家列表按钮点击事件
    GetTypeFunc_t Type_GetTypeName = nullptr; // 获取类型信息
    Object_FindObjectsOfType_t Object_FindObjectsOfType = NULL; // 查找场景中的对象
    GameUIController_Update_t o_GameUIController_Update = nullptr; // 原始的GameUIController::Update函数指针
    std::chrono::steady_clock::time_point g_last_update_timestamp; // 记录最后一次Update调用的时间戳

    // 新增的玩家坐标获取相关函数指针实例
    PlayerManager_get_allPlayerIds_t PlayerManager_get_allPlayerIds = nullptr;
    PlayerManager_FindIPlayer_t PlayerManager_FindIPlayer = nullptr;
    WorldMap_get_center_t WorldMap_get_center = nullptr;

    // List<System.Int32>实例
    List_Int32_t g_playerIdsList = nullptr;

    // 玩家名称获取相关函数指针实例
    IPlayerObject_get_displayName_t IPlayerObject_get_displayName = nullptr;

    // PlayerPlaceholder的m_map字段偏移
    size_t PlayerPlaceholder_m_map_offset = 0;

    // 任务队列，用于在主线程中执行操作
    std::vector<std::function<void()>> g_tasks;
    std::mutex g_tasks_mutex;

    // 玩家坐标信息结构
    struct PlayerCoordInfo {
        int32_t playerId;
        int32_t x;
        int32_t y;
        std::string displayName;
        bool isValid;

        PlayerCoordInfo() : playerId(0), x(0), y(0), displayName(""), isValid(false) {}
        PlayerCoordInfo(int32_t id, int32_t px, int32_t py, const std::string& name)
            : playerId(id), x(px), y(py), displayName(name), isValid(true) {}
    };

    // 玩家坐标列表和相关状态
    std::vector<PlayerCoordInfo> g_playerCoords;
    std::mutex g_playerCoords_mutex;
    bool g_showPlayerCoords = false;
    bool g_coordsUpdateInProgress = false;

    // 调试信息
    struct DebugInfo {
        std::string lastError;
        bool playerManagerFound = false;
        bool playerIdsListFound = false;
        int32_t playerCount = 0;
        int32_t processedPlayers = 0;
        int32_t successfulPlayers = 0;
        bool listCallsWorking = false;
        std::vector<std::string> stepLogs;

        void addLog(const std::string& message) {
            stepLogs.push_back(message);
            if (stepLogs.size() > 20) { // 保持最近20条日志
                stepLogs.erase(stepLogs.begin());
            }
        }

        void clear() {
            lastError.clear();
            playerManagerFound = false;
            playerIdsListFound = false;
            playerCount = 0;
            processedPlayers = 0;
            successfulPlayers = 0;
            listCallsWorking = false;
            stepLogs.clear();
        }
    };

    DebugInfo g_debugInfo;

    //================ 函数声明 ================//
    void Update(); // 任务处理函数
    void UpdatePlayerCoordinates(); // 更新玩家坐标信息
    void DrawPlayerCoordinatesUI(); // 绘制玩家坐标UI
    void* getPlayerManager(); // 获取PlayerManager静态实例
    int32_t callListGetCount(void* list); // 通用调用List.get_Count()
    int32_t callListGetItem(void* list, int32_t index); // 通用调用List.get_Item(index)

    //================ 核心功能实现 ================//

    /**
     * @brief 获取PlayerManager静态实例
     *        参考GameHelper::getGameServerManager的实现方式
     * @return PlayerManager实例指针，失败返回nullptr
     */
    void* getPlayerManager() {
        void* instance = nullptr;
        Il2CppGetStaticFieldValue("Assembly-CSharp.dll", "", "PlayerManager", "s_instance", &instance);
        if (instance != nullptr) {
            return instance;
        }
        return nullptr;
    }

    /**
     * @brief 通用调用List.get_Count()
     * @param list List对象指针
     * @return List长度，失败返回-1
     */
    int32_t callListGetCount(void* list) {
        if (!list) return -1;
        try {
            // 使用IL2CPP通用调用机制
            Il2CppCall_Int32_t countFunc = (Il2CppCall_Int32_t)Il2CppGetMethodOffset("mscorlib.dll", "System.Collections.Generic", "List`1", "get_Count", 0);
            if (countFunc) {
                return countFunc(list);
            }
            return -1;
        } catch (...) {
            return -1;
        }
    }

    /**
     * @brief 通用调用List.get_Item(index)
     * @param list List对象指针
     * @param index 索引
     * @return 元素值，失败返回0
     */
    int32_t callListGetItem(void* list, int32_t index) {
        if (!list) return 0;
        try {
            // 使用IL2CPP通用调用机制
            Il2CppCall_Int32_Int32_t itemFunc = (Il2CppCall_Int32_Int32_t)Il2CppGetMethodOffset("mscorlib.dll", "System.Collections.Generic", "List`1", "get_Item", 1);
            if (itemFunc) {
                return itemFunc(list, index);
            }
            return 0;
        } catch (...) {
            return 0;
        }
    }



    /**
     * @brief 向任务队列中添加一个新任务
     * @param task 要执行的任务函数
     */
    void AddTask(const std::function<void()>& task) {
        std::lock_guard<std::mutex> lock(g_tasks_mutex);
        g_tasks.push_back(task);
    }

    /**
     * @brief 更新玩家坐标信息
     *        通过PlayerManager获取所有玩家ID，然后获取每个玩家的世界坐标
     */
    void UpdatePlayerCoordinates() {
        if (g_coordsUpdateInProgress) return;
        g_coordsUpdateInProgress = true;

        // 清空调试信息
        g_debugInfo.clear();
        g_debugInfo.addLog("开始更新玩家坐标...");

        std::vector<PlayerCoordInfo> newCoords;

        try {
            // 步骤1: 获取PlayerManager静态实例
            g_debugInfo.addLog("步骤1: 获取PlayerManager实例...");
            void* playerManager = getPlayerManager();
            if (!playerManager) {
                g_debugInfo.lastError = "无法获取PlayerManager实例";
                g_debugInfo.addLog("错误: PlayerManager实例为空");
                g_coordsUpdateInProgress = false;
                return;
            }
            g_debugInfo.playerManagerFound = true;
            g_debugInfo.addLog("成功: PlayerManager实例获取成功");

            // 步骤2: 检查函数指针
            g_debugInfo.addLog("步骤2: 检查函数指针...");
            if (!PlayerManager_get_allPlayerIds) {
                g_debugInfo.lastError = "PlayerManager_get_allPlayerIds函数指针为空";
                g_debugInfo.addLog("错误: get_allPlayerIds函数指针为空");
                g_coordsUpdateInProgress = false;
                return;
            }

            // 步骤3: 获取玩家ID列表并保存到全局变量
            g_debugInfo.addLog("步骤3: 调用get_allPlayerIds...");
            g_playerIdsList = PlayerManager_get_allPlayerIds(playerManager);
            if (!g_playerIdsList) {
                g_debugInfo.lastError = "get_allPlayerIds返回空列表";
                g_debugInfo.addLog("错误: 玩家ID列表为空");
                g_coordsUpdateInProgress = false;
                return;
            }
            g_debugInfo.playerIdsListFound = true;
            g_debugInfo.addLog("成功: 玩家ID列表获取成功，保存到全局变量");

            // 步骤4: 获取真实玩家数量
            g_debugInfo.addLog("步骤4: 获取真实玩家数量...");
            int32_t playerCount = callListGetCount(g_playerIdsList);
            if (playerCount <= 0) {
                g_debugInfo.lastError = "无法获取玩家数量或玩家数量为0";
                g_debugInfo.addLog("错误: 玩家数量 = " + std::to_string(playerCount));
                g_debugInfo.addLog("可能原因: List调用函数未正确获取或List为空");
                g_coordsUpdateInProgress = false;
                return;
            }
            g_debugInfo.playerCount = playerCount;
            g_debugInfo.listCallsWorking = true;
            g_debugInfo.addLog("成功: 真实玩家数量 = " + std::to_string(playerCount));

            // 步骤5: 遍历真实玩家ID
            g_debugInfo.addLog("步骤5: 开始遍历真实玩家...");
            for (int32_t i = 0; i < playerCount; i++) { // 从0开始遍历List
                g_debugInfo.processedPlayers = i + 1;
                try {
                    // 获取真实玩家ID
                    int32_t playerId = callListGetItem(g_playerIdsList, i);
                    if (playerId == 0) {
                        g_debugInfo.addLog("跳过索引" + std::to_string(i) + ": 玩家ID为0");
                        continue;
                    }
                    g_debugInfo.addLog("处理真实玩家ID: " + std::to_string(playerId));

                    if (!PlayerManager_FindIPlayer) {
                        g_debugInfo.addLog("跳过玩家" + std::to_string(i) + ": FindIPlayer函数为空");
                        continue;
                    }

                    void* playerObject = PlayerManager_FindIPlayer(playerManager, playerId);
                    if (!playerObject) {
                        g_debugInfo.addLog("跳过玩家" + std::to_string(playerId) + ": 对象为空");
                        continue;
                    }

                    // 获取PlayerPlaceholder的WorldMap字段（通过字段偏移）
                    if (PlayerPlaceholder_m_map_offset == 0) {
                        g_debugInfo.addLog("跳过玩家" + std::to_string(playerId) + ": m_map偏移为0");
                        continue;
                    }

                    void* worldMap = *(void**)((char*)playerObject + PlayerPlaceholder_m_map_offset);
                    if (!worldMap) {
                        g_debugInfo.addLog("跳过玩家" + std::to_string(playerId) + ": WorldMap为空");
                        continue;
                    }

                    // 获取WorldMap的center坐标
                    if (!WorldMap_get_center) {
                        g_debugInfo.addLog("跳过玩家" + std::to_string(playerId) + ": get_center函数为空");
                        continue;
                    }

                    Vector2Int center = WorldMap_get_center(worldMap);

                    // 获取玩家显示名称
                    std::string playerName = "Player " + std::to_string(playerId); // 默认名称
                    if (IPlayerObject_get_displayName) {
                        String* displayNameStr = IPlayerObject_get_displayName(playerObject);
                        if (displayNameStr && displayNameStr->length > 0) {
                            // 使用Il2CppString的CString()方法转换
                            playerName = displayNameStr->CString();
                        }
                    }

                    // 创建坐标信息
                    newCoords.emplace_back(playerId, center.X, center.Y, playerName);
                    g_debugInfo.successfulPlayers++;

                    if (g_debugInfo.successfulPlayers <= 3) { // 只记录前3个成功的玩家
                        g_debugInfo.addLog("成功处理玩家" + std::to_string(playerId) + ": " + playerName + " (" + std::to_string(center.X) + "," + std::to_string(center.Y) + ")");
                    }

                } catch (...) {
                    g_debugInfo.addLog("异常: 处理玩家" + std::to_string(i) + "时发生异常");
                    continue;
                }
            }

            g_debugInfo.addLog("步骤7: 完成遍历，成功处理" + std::to_string(g_debugInfo.successfulPlayers) + "个玩家");

        } catch (...) {
            g_debugInfo.lastError = "发生未知异常";
            g_debugInfo.addLog("错误: 发生未知异常");
        }

        // 更新全局坐标列表
        {
            std::lock_guard<std::mutex> lock(g_playerCoords_mutex);
            g_playerCoords = std::move(newCoords);
        }

        g_debugInfo.addLog("更新完成，共获取" + std::to_string(newCoords.size()) + "个玩家坐标");
        g_coordsUpdateInProgress = false;
    }

    /**
     * @brief Hook GameUIController::Update 函数
     *        在此函数中调用我们的Update()来处理任务，然后调用原始函数
     * @param __this GameUIController 实例指针
     */
    void GameUIController_Update_Hook(void* __this) {
        g_GameUiController = __this; // 始终更新为当前实例
        g_status = Status::Ready;    // 只要Update被调用，就认为已就绪
        g_last_update_timestamp = std::chrono::steady_clock::now(); // 更新时间戳

        Update(); // 处理我们的任务队列

        // 调用原始的Update函数
        if (o_GameUIController_Update) {
            o_GameUIController_Update(__this);
        }
    }

    /**
     * @brief 绘制玩家坐标UI
     */
    void DrawPlayerCoordinatesUI() {
        if (!g_showPlayerCoords) return;

        ImGui::Begin("玩家世界坐标 - 调试版", &g_showPlayerCoords, ImGuiWindowFlags_AlwaysAutoResize);

        if (ImGui::Button("刷新坐标")) {
            AddTask([]() {
                UpdatePlayerCoordinates();
            });
        }

        ImGui::SameLine();
        if (g_coordsUpdateInProgress) {
            ImGui::TextColored(ImVec4(1, 1, 0, 1), "更新中...");
        } else {
            ImGui::TextColored(ImVec4(0, 1, 0, 1), "就绪");
        }

        ImGui::Separator();

        // 调试信息区域
        if (ImGui::CollapsingHeader("调试信息", ImGuiTreeNodeFlags_DefaultOpen)) {
            // 状态信息
            ImGui::Text("PlayerManager实例: %s", g_debugInfo.playerManagerFound ? "✓ 找到" : "✗ 未找到");
            ImGui::Text("玩家ID列表: %s", g_debugInfo.playerIdsListFound ? "✓ 找到" : "✗ 未找到");
            ImGui::Text("玩家总数: %d", g_debugInfo.playerCount);
            ImGui::Text("已处理: %d", g_debugInfo.processedPlayers);
            ImGui::Text("成功获取: %d", g_debugInfo.successfulPlayers);
            ImGui::Text("List调用: %s", g_debugInfo.listCallsWorking ? "✓ 正常" : "✗ 失败");

            if (!g_debugInfo.lastError.empty()) {
                ImGui::TextColored(ImVec4(1, 0, 0, 1), "最后错误: %s", g_debugInfo.lastError.c_str());
            }

            ImGui::Separator();

            // 函数指针状态
            if (ImGui::CollapsingHeader("函数指针状态")) {
                ImGui::Text("PlayerManager_get_allPlayerIds: %s", PlayerManager_get_allPlayerIds ? "✓" : "✗");
                ImGui::Text("PlayerManager_FindIPlayer: %s", PlayerManager_FindIPlayer ? "✓" : "✗");
                ImGui::Text("WorldMap_get_center: %s", WorldMap_get_center ? "✓" : "✗");
                ImGui::Text("IPlayerObject_get_displayName: %s", IPlayerObject_get_displayName ? "✓" : "✗");
                ImGui::Text("PlayerPlaceholder_m_map_offset: %zu", PlayerPlaceholder_m_map_offset);
                ImGui::Text("g_playerIdsList: %s", g_playerIdsList ? "✓ 已获取" : "✗ 未获取");
            }

            ImGui::Separator();

            // 步骤日志
            if (ImGui::CollapsingHeader("执行日志")) {
                for (const auto& log : g_debugInfo.stepLogs) {
                    ImGui::Text("%s", log.c_str());
                }
            }
        }

        ImGui::Separator();

        // 显示玩家坐标列表
        {
            std::lock_guard<std::mutex> lock(g_playerCoords_mutex);
            if (g_playerCoords.empty()) {
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1), "暂无玩家坐标数据");
            } else {
                // 表格标题
                ImGui::Columns(4, "PlayerCoords");
                ImGui::Text("玩家ID");
                ImGui::NextColumn();
                ImGui::Text("玩家名称");
                ImGui::NextColumn();
                ImGui::Text("X坐标");
                ImGui::NextColumn();
                ImGui::Text("Y坐标");
                ImGui::NextColumn();
                ImGui::Separator();

                // 显示每个玩家的坐标
                for (const auto& coord : g_playerCoords) {
                    if (coord.isValid) {
                        ImGui::Text("%d", coord.playerId);
                        ImGui::NextColumn();
                        ImGui::Text("%s", coord.displayName.c_str());
                        ImGui::NextColumn();
                        ImGui::Text("%d", coord.x);
                        ImGui::NextColumn();
                        ImGui::Text("%d", coord.y);
                        ImGui::NextColumn();
                    }
                }
                ImGui::Columns(1);
            }
        }

        ImGui::End();
    }

    /**
     * @brief 绘制ImGui界面
     */
    void DrawUI() {
        // 状态显示
        switch (g_status) {
            case Status::NotReady:
                ImGui::TextColored(ImVec4(1, 0.5f, 0, 1), "状态: 未初始化...");
                break;
            case Status::Preparing:
                ImGui::TextColored(ImVec4(1, 1, 0, 1), "状态: 等待游戏场景加载...");
                break;
            case Status::FieldNotFound:
                ImGui::TextColored(ImVec4(1, 0, 0, 1), "状态: 错误, 方法未找到!");
                break;
            case Status::Ready:
                ImGui::TextColored(ImVec4(0, 1, 0, 1), "状态: 已就绪");
                break;
        }
        ImGui::Separator();

        // 功能就绪后才显示相关按钮
        if (g_status == Status::Ready) {
            if (ImGui::Button("打开玩家列表")) {
                AddTask([]() {
                    if (g_GameUiController && PlayerListButtonClicked) {
                        PlayerListButtonClicked(g_GameUiController);
                    }
                });
            }

            // 新增：玩家坐标功能按钮
            ImGui::SameLine();
            if (ImGui::Button("显示玩家坐标")) {
                g_showPlayerCoords = true;
                AddTask([]() {
                    UpdatePlayerCoordinates();
                });
            }
        }

        // 绘制玩家坐标窗口
        DrawPlayerCoordinatesUI();
    }
    
    /**
     * @brief 后台循环，可用于执行需要持续运行的逻辑
     *        在这里通过检查时间戳来判断实例是否还存活
     */
    void Loop() {
        // 如果状态为就绪，但长时间未收到Update心跳，则认为实例已失效
        if (g_status == Status::Ready) {
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - g_last_update_timestamp).count();
            if (elapsed_ms > 500) { // 超过500毫秒未更新，认为实例丢失
                g_GameUiController = nullptr;
                g_status = Status::Preparing;
            }
        }
    }

    /**
     * @brief 处理任务队列中的所有任务
     *        通过将任务列表交换到本地变量来减少锁的持有时间
     */
    void Update() {
        std::vector<std::function<void()>> tasks_to_run;
        {
            std::lock_guard<std::mutex> lock(g_tasks_mutex);
            if (!g_tasks.empty()) {
                tasks_to_run.swap(g_tasks);
            }
        }

        for (const auto& task : tasks_to_run) {
            task();
        }
    }

    /**
     * @brief 总初始化函数，在注入后尽早调用
     *        用于获取函数地址并Hook
     */
    void Init() {
        g_status = Status::NotReady;

        // 获取必要的函数指针
        PlayerListButtonClicked = (PlayerListButtonClicked_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "GameUIController", "PlayerListButtonClicked", 0);
        void* update_method = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "GameUIController", "Update", 0);

        // 获取玩家坐标相关的函数指针
        PlayerManager_get_allPlayerIds = (PlayerManager_get_allPlayerIds_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerManager", "get_allPlayerIds", 0);
        PlayerManager_FindIPlayer = (PlayerManager_FindIPlayer_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerManager", "FindIPlayer", 1);
        WorldMap_get_center = (WorldMap_get_center_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "WorldMap", "get_center", 0);



        // 获取玩家名称相关函数指针
        IPlayerObject_get_displayName = (IPlayerObject_get_displayName_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "IPlayerObject", "get_displayName", 0);

        // 获取PlayerPlaceholder的m_map字段偏移
        PlayerPlaceholder_m_map_offset = Il2CppGetFieldOffset("Assembly-CSharp.dll", "", "PlayerPlaceholder", "m_map");

        if (!PlayerListButtonClicked || !update_method) {
            g_status = Status::FieldNotFound;
            return;
        }

        // 检查玩家坐标相关函数是否获取成功（非必需，不影响基本功能）
        g_debugInfo.addLog("=== 函数指针检查 ===");
        g_debugInfo.addLog("PlayerManager_get_allPlayerIds: " + std::string(PlayerManager_get_allPlayerIds ? "✓" : "✗"));
        g_debugInfo.addLog("PlayerManager_FindIPlayer: " + std::string(PlayerManager_FindIPlayer ? "✓" : "✗"));
        g_debugInfo.addLog("WorldMap_get_center: " + std::string(WorldMap_get_center ? "✓" : "✗"));
        g_debugInfo.addLog("IPlayerObject_get_displayName: " + std::string(IPlayerObject_get_displayName ? "✓" : "✗"));
        g_debugInfo.addLog("PlayerPlaceholder_m_map_offset: " + std::to_string(PlayerPlaceholder_m_map_offset));

        if (!PlayerManager_get_allPlayerIds || !PlayerManager_FindIPlayer ||
            !WorldMap_get_center || PlayerPlaceholder_m_map_offset == 0) {
            g_debugInfo.addLog("警告: 部分函数指针获取失败");
        } else {
            g_debugInfo.addLog("成功: 所有必需函数指针获取成功");
        }

        // Hook GameUIController::Update 方法
        if (DobbyHook(update_method, (void*)GameUIController_Update_Hook, (void**)&o_GameUIController_Update) == 0) {
            g_status = Status::Preparing; // Hook成功，等待Update的第一次调用
            g_last_update_timestamp = std::chrono::steady_clock::now();
        } else {
            g_status = Status::FieldNotFound; // Hook失败
        }
    }
} 