#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include <vector>
#include <mutex>
#include <string>
#include <map>
#include <algorithm>
#include "Unity/Vector2.hpp"
#include "Unity/Vector3.hpp"

namespace DrawHack {

    // --- 玩家射线功能模块 ---
    namespace PlayerRays {
        // --- 线程安全与数据 ---
        std::mutex g_render_data_mutex;
        std::vector<ImVec2> g_player_screen_positions; 

        // --- 功能开关与参数 ---
        bool g_enable_esp = false;
        float g_line_thickness = 1.5f;
        float g_y_offset = 50.0f; 
        bool g_exclude_self = true;

        // 函数声明
        void Loop();
        void Render();
        void DrawUI();
    }

    // --- 物品绘制功能模块 ---
    namespace ItemESP {
        // --- 线程安全与数据 ---
        struct ItemRenderInfo {
            ImVec2 screen_pos;
            int id;
        };
        std::mutex g_render_data_mutex;
        std::vector<ItemRenderInfo> g_item_render_info;

        std::mutex g_counter_mutex;
        std::map<int, int> g_item_counts;

        // --- 功能开关与参数 ---
        bool g_enable_esp = false;
        bool g_enable_lines = false;
        bool g_show_counter = false;
        bool g_show_only_home_markers = false; // 新增
        float g_font_size = 14.0f;
        float g_line_thickness = 1.0f;

        // --- ID 过滤器数据 ---
        char g_id_input_buffer[20] = ""; // 增大缓冲区以适应更长的文本
        std::vector<int> g_id_filter_list;    
        bool g_is_blacklist_mode = false;
        // g_show_virtual_keyboard is removed

        // 函数声明
        void Loop();
        void Render();
        void RenderCounter();
        void DrawUI();
    }

    // --- 物品修改功能模块 ---
    namespace ObjectModifiers {
        // --- 功能开关与数据 ---
        bool g_enable_force_collidable = false;
        int32_t g_collidable_field_offset = -1;

        // 函数声明
        void OnUpdate();
        void DrawUI();
    }


    // --- 通用游戏数据与函数指针 ---
    void* g_playerControllerType = nullptr;
    void* g_objectControllerType = nullptr; // 新增

    Array<void**>*(*Object_FindObjectsOfType)(void*) = nullptr;
    typedef void* (*GetTypeFunc_t)(String*);
    GetTypeFunc_t Type_GetTypeName = nullptr;
    void* (*Camera_get_main)() = nullptr;
    Vector3 (*Camera_WorldToScreenPoint)(void*, Vector3) = nullptr;
    // Player
    bool (*PlayerController_get_isSelfPlayer)(void*) = nullptr;
    Vector2 (*PlayerController_get_position)(void*) = nullptr;
    // Object
    Vector2 (*ObjectController_get_gridWdPos)(void*) = nullptr; // 新增
    int (*ObjectController_get_id)(void*) = nullptr;          // 新增
    bool (*ObjectController_get_isHomeMarker)(void*) = nullptr; // 新增

    // 主初始化 (所有模块共用)
    void Init() {
        Object_FindObjectsOfType = (Array<void**>*(*)(void*))Il2CppGetMethodOffset("UnityEngine.dll", "UnityEngine", "Object", "FindObjectsOfType", 1);
        Type_GetTypeName = (GetTypeFunc_t)Il2CppGetMethodOffset("mscorlib.dll", "System", "Type", "GetType", 1);
        Camera_get_main = (void* (*)())Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_main", 0);
        Camera_WorldToScreenPoint = (Vector3 (*)(void*, Vector3))Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "WorldToScreenPoint", 1);
        PlayerController_get_isSelfPlayer = (bool (*)(void*))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "get_isSelfPlayer", 0);
        PlayerController_get_position = (Vector2 (*)(void*))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "get_position", 0);
        
        // 新增 ObjectController 相关
        ObjectController_get_gridWdPos = (Vector2 (*)(void*))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "ObjectController", "get_gridWdPos", 0);
        ObjectController_get_id = (int (*)(void*))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "ObjectController", "get_id", 0);
        ObjectController_get_isHomeMarker = (bool (*)(void*))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "ObjectController", "get_isHomeMarker", 0); // 新增
        
        // 新增 System Keyboard 相关 (使用正确的DLL)
        // TouchScreenKeyboard_Open = (void* (*)(String*, int, bool, bool, bool, bool, String*, int))Il2CppGetMethodOffset("UnityEngine.dll", "UnityEngine", "TouchScreenKeyboard", "Open", 8);
        // TouchScreenKeyboard_get_active = (bool (*)(void*))Il2CppGetMethodOffset("UnityEngine.dll", "UnityEngine", "TouchScreenKeyboard", "get_active", 0);
        // TouchScreenKeyboard_get_text = (String* (*)(void*))Il2CppGetMethodOffset("UnityEngine.dll", "UnityEngine", "TouchScreenKeyboard", "get_text", 0);

        // 新增 ObjectModifiers 相关
        ObjectModifiers::g_collidable_field_offset = GameHelper::GetFieldOffset("Assembly-CSharp.dll", "", "ObjectController", "m_collidable");

        if (Type_GetTypeName) {
            g_playerControllerType = Type_GetTypeName(Il2CppString::Create("PlayerController, Assembly-CSharp"));
            g_objectControllerType = Type_GetTypeName(Il2CppString::Create("ObjectController, Assembly-CSharp"));
        }
    }

    // --- 玩家射线功能实现 ---
    namespace PlayerRays {
        void Loop() {
            // 这个向量将持有当前帧的结果。
            // 如果ESP关闭或找不到玩家，它将保持为空。
            std::vector<ImVec2> current_frame_positions;

            // --- 1. 仅在ESP启用时才进行处理 ---
            if (g_enable_esp) {
                void* mainCamera = Camera_get_main ? Camera_get_main() : nullptr;
                
                if (mainCamera) {
                    Array<void**>* allPlayers_raw = (g_playerControllerType && Object_FindObjectsOfType)
                                                ? Object_FindObjectsOfType(g_playerControllerType) : nullptr;
                    
                    // 进行极度严谨的防御性检查，防止因API在错误场景下返回垃圾指针而导致崩溃
                    if (allPlayers_raw && allPlayers_raw->getPointer() && allPlayers_raw->getLength() > 0) {
                        
                        // --- 2. 创建安全的指针副本 ---
                        std::vector<void*> allPlayers_copy;
                        allPlayers_copy.reserve(allPlayers_raw->getLength());
                        for (int i = 0; i < allPlayers_raw->getLength(); ++i) {
                            allPlayers_copy.push_back(allPlayers_raw->getPointer()[i]);
                        }

                        // --- 3. 在安全的副本上处理所有逻辑 ---
                        void* localPlayer = nullptr;
                        if (PlayerController_get_isSelfPlayer) {
                            for (void* p : allPlayers_copy) {
                                if (p && PlayerController_get_isSelfPlayer(p)) {
                                    localPlayer = p;
                                    break;
                                }
                            }
                        }

                        float screen_height = ImGui::GetIO().DisplaySize.y;
                        if (screen_height > 0) {
                            for (void* player : allPlayers_copy) {
                                if (!player || (g_exclude_self && player == localPlayer)) continue;


                                Vector2 world_pos_2d = PlayerController_get_position(player);
                                Vector3 world_pos_3d = { world_pos_2d.X, world_pos_2d.Y, 0.0f };
                                Vector3 screen_pos_3d = Camera_WorldToScreenPoint(mainCamera, world_pos_3d);

                                if (screen_pos_3d.Z > 0) {
                                    // 填充临时列表
                                    current_frame_positions.push_back(ImVec2(screen_pos_3d.X, screen_height - screen_pos_3d.Y - g_y_offset));
                                }
                            }
                        }
                    } 
                } 
            } 

            // --- 4. 原子地更新全局渲染数据 ---
            // 这是修改 g_player_screen_positions 的唯一位置
            {
                std::lock_guard<std::mutex> lock(g_render_data_mutex);
                g_player_screen_positions = current_frame_positions;
            }
        }

        void Render() {
            std::vector<ImVec2> positions_to_draw;
            {
                std::lock_guard<std::mutex> lock(g_render_data_mutex);
                if (!g_enable_esp || g_player_screen_positions.empty()) {
                    return;
                }
                positions_to_draw = g_player_screen_positions;
            }

            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            float screen_width = ImGui::GetIO().DisplaySize.x;
            ImVec2 line_origin = ImVec2(screen_width / 2.0f, 0.0f);

            for (const auto& pos : positions_to_draw) {
                drawList->AddLine(line_origin, pos, IM_COL32(255, 0, 0, 255), g_line_thickness);
            }
        }

        void DrawUI() {
            ImGui::Checkbox("绘制玩家射线", &g_enable_esp);
            ImGui::SameLine();
            ImGui::TextDisabled("(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("从屏幕顶部中心向其他所有玩家绘制一条红线");
            }
            ImGui::Checkbox("排除自身", &g_exclude_self);
            ImGui::SliderFloat("线条粗细", &g_line_thickness, 1.0f, 10.0f, "%.1f");
            ImGui::SliderFloat("Y轴向上偏移", &g_y_offset, 0.0f, 300.0f, "%.0f px");
        }
    }

    // --- 物品绘制功能实现 ---
    namespace ItemESP {
        void Loop() {
            std::vector<ItemRenderInfo> current_frame_info;
            std::map<int, int> current_item_counts;

            // 只要有任何一个功能开启，就需要获取对象列表
            if (g_enable_esp || g_show_counter) {
                void* mainCamera = Camera_get_main ? Camera_get_main() : nullptr;
                if (mainCamera) {
                    Array<void**>* allObjects_raw = (g_objectControllerType && Object_FindObjectsOfType)
                                                 ? Object_FindObjectsOfType(g_objectControllerType) : nullptr;
                    
                    if (allObjects_raw && allObjects_raw->getPointer() && allObjects_raw->getLength() > 0) {
                        std::vector<void*> allObjects_copy;
                        allObjects_copy.reserve(allObjects_raw->getLength());
                        for (int i = 0; i < allObjects_raw->getLength(); ++i) {
                            allObjects_copy.push_back(allObjects_raw->getPointer()[i]);
                        }

                        float screen_height = ImGui::GetIO().DisplaySize.y;
                        if (screen_height > 0) {
                            for (void* obj : allObjects_copy) {
                                if (!obj) continue;

                                // --- 应用过滤器 ---
                                if (g_show_only_home_markers) {
                                    if (!ObjectController_get_isHomeMarker || !ObjectController_get_isHomeMarker(obj)) {
                                        continue; 
                                    }
                                }

                                int id = ObjectController_get_id(obj);

                                if (!g_id_filter_list.empty()) {
                                    bool found_in_list = std::find(g_id_filter_list.begin(), g_id_filter_list.end(), id) != g_id_filter_list.end();
                                    
                                    if (g_is_blacklist_mode) { // 黑名单模式
                                        if (found_in_list) continue; // 如果ID在黑名单中，则跳过
                                    } else { // 白名单模式
                                        if (!found_in_list) continue; // 如果ID不在白名单中，则跳过
                                    }
                                }
                                // --- 过滤器结束 ---

                                current_item_counts[id]++; 

                                if (g_enable_esp) {
                                    Vector2 world_pos_2d = ObjectController_get_gridWdPos(obj);
                                    Vector3 world_pos_3d = { world_pos_2d.X, world_pos_2d.Y, 0.0f };
                                    Vector3 screen_pos_3d = Camera_WorldToScreenPoint(mainCamera, world_pos_3d);

                                    if (screen_pos_3d.Z > 0) {
                                        current_frame_info.push_back({
                                            ImVec2(screen_pos_3d.X, screen_height - screen_pos_3d.Y),
                                            id
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 原子更新渲染数据
            {
                std::lock_guard<std::mutex> lock(g_render_data_mutex);
                g_item_render_info = current_frame_info;
            }
            // 原子更新统计数据
            {
                std::lock_guard<std::mutex> lock(g_counter_mutex);
                g_item_counts = current_item_counts;
            }
        }

        void Render() {
            std::vector<ItemRenderInfo> info_to_draw;
            {
                std::lock_guard<std::mutex> lock(g_render_data_mutex);
                if (!g_enable_esp || g_item_render_info.empty()) {
                    return;
                }
                info_to_draw = g_item_render_info;
            }

            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            ImVec2 line_origin = ImVec2(ImGui::GetIO().DisplaySize.x / 2.0f, 0.0f);

            for (const auto& info : info_to_draw) {
                if (g_enable_lines) {
                    drawList->AddLine(line_origin, info.screen_pos, IM_COL32(255, 255, 0, 180), g_line_thickness);
                }
                std::string text = std::to_string(info.id);
                drawList->AddText(nullptr, g_font_size, info.screen_pos, IM_COL32(255, 255, 0, 255), text.c_str());
            }
        }

        void RenderCounter() {
            if (!g_show_counter) return;
            
            std::map<int, int> counts_to_draw;
            {
                std::lock_guard<std::mutex> lock(g_counter_mutex);
                if (g_item_counts.empty()) return;
                counts_to_draw = g_item_counts;
            }

            ImDrawList* drawList = ImGui::GetForegroundDrawList();
            ImVec2 pos = ImVec2(10.0f, 10.0f);
            float line_height = ImGui::GetTextLineHeightWithSpacing();
            ImU32 text_color = IM_COL32(255, 255, 255, 255);
            ImU32 bg_color = IM_COL32(0, 0, 0, 150);

            for (const auto& pair : counts_to_draw) {
                std::string text = "ID: " + std::to_string(pair.first) + " | Count: " + std::to_string(pair.second);
                ImVec2 text_size = ImGui::CalcTextSize(text.c_str());
                drawList->AddRectFilled(pos, ImVec2(pos.x + text_size.x + 4, pos.y + text_size.y + 2), bg_color);
                drawList->AddText(ImVec2(pos.x + 2, pos.y + 1), text_color, text.c_str());
                pos.y += line_height;
            }
        }

        void DrawUI() {
            ImGui::Checkbox("绘制物品ID", &g_enable_esp);
            ImGui::SameLine();
            ImGui::Checkbox("绘制射线", &g_enable_lines);
            ImGui::Checkbox("显示物品统计", &g_show_counter);
            ImGui::SliderFloat("字体大小", &g_font_size, 8.0f, 24.0f, "%.0f");
            ImGui::SliderFloat("射线粗细", &g_line_thickness, 1.0f, 10.0f, "%.1f");

            ImGui::Separator();
            ImGui::Text("过滤器:");
            ImGui::Checkbox("只显示家园标记", &g_show_only_home_markers);
            
            ImGui::Separator();
            ImGui::Text("ID 过滤器:");
            ImGui::Checkbox("启用黑名单模式", &g_is_blacklist_mode);

            // 点击按钮来请求中央虚拟键盘
            std::string btn_text = "ID: ";
            btn_text += (g_id_input_buffer[0] == '\0' ? "Tap to enter..." : g_id_input_buffer);
            if (ImGui::Button(btn_text.c_str(), ImVec2(ImGui::GetContentRegionAvail().x * 0.7f, 0))) {
                // 恢复 XwzcMods:: 前缀
                XwzcMods::g_keyboard_target_buffer = g_id_input_buffer;
                XwzcMods::g_keyboard_target_buffer_size = sizeof(g_id_input_buffer);
                XwzcMods::g_keyboard_title = "输入物品ID";
                XwzcMods::g_show_virtual_keyboard = true;
            }
            
            ImGui::SameLine();
            if (ImGui::Button("添加", ImVec2(-1, 0))) {
                int id_to_add = std::atoi(g_id_input_buffer);
                if (id_to_add > 0 && std::find(g_id_filter_list.begin(), g_id_filter_list.end(), id_to_add) == g_id_filter_list.end()) {
                    g_id_filter_list.push_back(id_to_add);
                }
                g_id_input_buffer[0] = '\0'; 
            }
            
            // 显示当前已添加的ID列表
            int id_to_remove = -1;
            for (int i = 0; i < g_id_filter_list.size(); ++i) {
                ImGui::Text("  - %d", g_id_filter_list[i]);
                ImGui::SameLine();
                if (ImGui::SmallButton(("移除##" + std::to_string(i)).c_str())) {
                    id_to_remove = i;
                }
            }
            if (id_to_remove != -1) {
                g_id_filter_list.erase(g_id_filter_list.begin() + id_to_remove);
            }
        }
    }

    // --- 物品修改功能实现 ---
    namespace ObjectModifiers {
        void OnUpdate() {
            if (!g_enable_force_collidable || g_collidable_field_offset == -1) {
                return;
            }

            Array<void**>* allObjects_raw = (g_objectControllerType && Object_FindObjectsOfType)
                                         ? Object_FindObjectsOfType(g_objectControllerType) : nullptr;

            if (allObjects_raw && allObjects_raw->getPointer() && allObjects_raw->getLength() > 0) {
                std::vector<void*> allObjects_copy;
                allObjects_copy.reserve(allObjects_raw->getLength());
                for (int i = 0; i < allObjects_raw->getLength(); ++i) {
                    allObjects_copy.push_back(allObjects_raw->getPointer()[i]);
                }

                for (void* obj : allObjects_copy) {
                    if (obj) {
                        *reinterpret_cast<bool*>(reinterpret_cast<uintptr_t>(obj) + g_collidable_field_offset) = true;
                    }
                }
            }
        }

        void DrawUI() {
            ImGui::Checkbox("强制所有物品可碰撞", &g_enable_force_collidable);
            if (g_collidable_field_offset == -1) {
                ImGui::SameLine();
                ImGui::TextDisabled("(错误: 未找到字段)");
            }
        }
    }


    // --- 主循环、渲染、UI ---
    void OnUpdate() {
        PlayerRays::Loop();
        ItemESP::Loop();
        ObjectModifiers::OnUpdate(); // 新增调用
    }

    void Loop() {
        PlayerRays::Loop();
        ItemESP::Loop();
    }

    void Render() {
        PlayerRays::Render();
        ItemESP::Render();
        ItemESP::RenderCounter();
    }

    void DrawUI() {
        if (ImGui::CollapsingHeader("玩家射线")) {
            PlayerRays::DrawUI();
        }
        if (ImGui::CollapsingHeader("物品绘制")) {
            ItemESP::DrawUI();
        }
        if (ImGui::CollapsingHeader("物品修改")) {
            ObjectModifiers::DrawUI(); // 新增调用
        }
        // 键盘的绘制将移至 XwzcMods.h
    }
}
