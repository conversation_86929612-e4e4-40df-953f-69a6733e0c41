//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include "dobby.h"
#include <cstdint>
#include <vector>
#include <string>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <string>
#include <vector>
#include <functional>
#include <mutex>
#include "Unity/Vector2.hpp"
#include "Unity/Vector3.hpp"
#include "Unity/Vector2Int.hpp"

namespace PlayerSelfHack {

    // 全局变量
    Array<void**>* g_allCameras = nullptr;
    void* g_cameraType = nullptr;
    float g_ortho_size = 5.0f; // 默认视口大小
    bool g_initial_size_set = false;

    // 速度修改 (新增功能)
    bool g_speed_hack_enabled = false;
    float g_player_speed = 5.0f; // 默认速度
    void* g_local_player_instance = nullptr;

    // 出生位置修改相关变量
    bool g_spawn_position_hack_enabled = false;
    int g_spawn_x = 0;
    int g_spawn_y = 0;
    char g_spawn_x_buffer[32] = "0";
    char g_spawn_y_buffer[32] = "0";

    // 网络日志相关变量
    bool g_network_log_enabled = false;
    std::vector<std::string> g_network_log_messages;
    bool g_show_network_log_window = false;
    int g_max_log_messages = 100;  // 最大日志条数
    void* g_remoteIOHandle_outputString_address = nullptr;  // 存储获取到的地址
    bool g_remoteIOHandle_outputString_hooked = false;      // 是否成功Hook
    void* g_playerStatusUpdate_execute_address = nullptr;   // 出生位置Hook地址
    bool g_playerStatusUpdate_execute_hooked = false;       // 出生位置Hook状态

    // CommandController相关
    void* g_commandControllerType = nullptr;                // CommandController类型
    void* g_commandController_instance = nullptr;           // CommandController实例
    void* g_remoteIOHandle_instance = nullptr;              // IRemoteIOHandle实例 (m_sender字段)

    // 函数指针
    typedef void* (*GetTypeFunc_t)(String*);
    typedef float (*get_orthographicSize_t)(void*);
    typedef void (*set_orthographicSize_t)(void*, float);
    Array<void**>*(*Object_FindObjectsOfType)(void*) = NULL;

    GetTypeFunc_t Type_GetTypeName = nullptr;
    get_orthographicSize_t get_orthographicSize = nullptr;
    set_orthographicSize_t set_orthographicSize = nullptr;

    // Speed (基础功能保留)
    void (*set_speed_ptr)(void*, float) = nullptr;
    bool (*get_isSelfPlayer_ptr)(void*) = nullptr;
    void (*PlayerController_Update_o)(void*) = nullptr; // 原始Update

    // 出生位置修改Hook相关函数指针
    void (*PlayerStatusUpdate_Execute_o)(void*, void*) = nullptr;

    // 网络日志Hook相关函数指针
    void (*IRemoteIOHandle_OutputString_o)(void*, void*) = nullptr;

    // 前向声明
    void DrawNetworkLogWindow();
    void InitNetworkLogHook();
    void UpdateCommandControllerInstance();

    // Common
    void* g_playerControllerType = nullptr;

    void (*CommandController_PlayerMove_ptr)(void*, Vector2Int, int) = nullptr;

    // 修改摄像机视野
    void ChangeCameraFov(float size) {
        // 只修改第二个摄像机（索引为1）
        if (g_allCameras && g_allCameras->getLength() > 1) {
            void* secondCamera = g_allCameras->getPointer()[1];
            if (secondCamera) {
                set_orthographicSize(secondCamera, size);
            }
        }
    }

    // PlayerStatusUpdate::Execute的Hook函数
    void PlayerStatusUpdate_Execute_Hook(void* playerController, void* statusUpdate) {
        // 检查是否为自己的玩家且启用了出生位置修改
        if (g_spawn_position_hack_enabled && get_isSelfPlayer_ptr && get_isSelfPlayer_ptr(playerController)) {
            // 修改PlayerStatusUpdate中的大地图坐标
            // destX偏移100, destY偏移104
            *(int32_t*)((uintptr_t)statusUpdate + 100) = g_spawn_x;  // destX
            *(int32_t*)((uintptr_t)statusUpdate + 104) = g_spawn_y;  // destY

            // 同时修改动作目标坐标，确保一致性
            // actionTargetX偏移48, actionTargetY偏移52
            *(int32_t*)((uintptr_t)statusUpdate + 48) = g_spawn_x;   // actionTargetX
            *(int32_t*)((uintptr_t)statusUpdate + 52) = g_spawn_y;   // actionTargetY
        }

        // 调用原始函数
        if (PlayerStatusUpdate_Execute_o) {
            PlayerStatusUpdate_Execute_o(playerController, statusUpdate);
        }
    }



    // 新增：PlayerController::Update 的Hook函数
    void PlayerController_Update_Hook(void* this_ptr) {
        // 检查当前Update的实例是否是我们找到的本地玩家
        if (g_local_player_instance == this_ptr && g_speed_hack_enabled) {
            if (set_speed_ptr) {
                set_speed_ptr(this_ptr, g_player_speed);
            }
        }

        // 调用原始的Update函数，确保其他逻辑正常运行
        if (PlayerController_Update_o) {
            PlayerController_Update_o(this_ptr);
        }
    }

    // 应用出生位置设置
    void ApplySpawnPosition() {
        g_spawn_x = atoi(g_spawn_x_buffer);
        g_spawn_y = atoi(g_spawn_y_buffer);
    }

    // IRemoteIOHandle::OutputString的Hook函数 - 记录网络发送数据
    void IRemoteIOHandle_OutputString_Hook(void* handle, void* output) {
        if (g_network_log_enabled && output) {
            // 获取字符串内容
            typedef const char* (*String_get_chars_t)(void*);
            String_get_chars_t getString = (String_get_chars_t)0x17B2E30;  // String.get_Chars

            const char* message = getString(output);
            if (message) {
                // 添加时间戳
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now.time_since_epoch()) % 1000;

                std::stringstream ss;
                ss << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S");
                ss << "." << std::setfill('0') << std::setw(3) << ms.count() << "] ";
                ss << "SEND: " << message;

                // 添加到日志列表
                g_network_log_messages.push_back(ss.str());

                // 限制日志条数
                if (g_network_log_messages.size() > g_max_log_messages) {
                    g_network_log_messages.erase(g_network_log_messages.begin());
                }
            }
        }

        // 调用原始函数
        if (IRemoteIOHandle_OutputString_o) {
            IRemoteIOHandle_OutputString_o(handle, output);
        }
    }



    void InitCommandController() {
        if (!g_commandControllerType) {
            if (Type_GetTypeName) {
                g_commandControllerType = Type_GetTypeName(Il2CppString::Create("CommandController, Assembly-CSharp"));
            }
        }
        
        if (g_commandControllerType && Object_FindObjectsOfType) {
            Array<void**>* commandControllers = Object_FindObjectsOfType(g_commandControllerType);
            if (commandControllers && commandControllers->getLength() > 0) {
                g_commandController_instance = commandControllers->getPointer()[0];

                if (g_commandController_instance) {
                    CommandController_PlayerMove_ptr = (void (*)(void*, Vector2Int, int))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "CommandController", "PlayerMove", 2);
                }
            }
        }
    }

    void DoTestMove() {
        if (!g_commandController_instance || !CommandController_PlayerMove_ptr || !g_local_player_instance) {
            return;
        }

        Vector2Int testGridPos = { 0, 0 };

        CommandController_PlayerMove_ptr(g_commandController_instance, testGridPos, 0);
    }

    // 绘制UI
    void DrawUI() {
        // --- Camera FOV UI ---
        ImGui::Separator();
        if (ImGui::SliderFloat("视野大小", &g_ortho_size, 1.0f, 20.0f)) {
            ChangeCameraFov(g_ortho_size);
        }

        // --- Speed Hack UI ---
        ImGui::Separator();
        ImGui::Checkbox("启用基础速度修改", &g_speed_hack_enabled);
        if (ImGui::SliderFloat("玩家速度", &g_player_speed, 1.0f, 20.0f, "%.1f")) {
            // 滑块拖动时实时生效
        }
        ImGui::Text("本地玩家实例: %p", g_local_player_instance);

        // --- 出生位置修改 UI ---
        ImGui::Separator();
        ImGui::Text("--- 出生位置修改 ---");
        ImGui::Checkbox("启用出生位置修改", &g_spawn_position_hack_enabled);

        if (g_spawn_position_hack_enabled) {
            ImGui::Text("设置出生坐标 (大地图坐标):");

            // X坐标输入
            ImGui::Text("X坐标:");
            ImGui::SameLine();
            if (ImGui::Button("输入X##spawn_x")) {
                XwzcMods::g_keyboard_target_buffer = g_spawn_x_buffer;
                XwzcMods::g_keyboard_target_buffer_size = sizeof(g_spawn_x_buffer);
                XwzcMods::g_keyboard_title = "输入出生X坐标";
                XwzcMods::g_show_virtual_keyboard = true;
            }
            ImGui::SameLine();
            ImGui::Text("当前: %s", g_spawn_x_buffer);

            // Y坐标输入
            ImGui::Text("Y坐标:");
            ImGui::SameLine();
            if (ImGui::Button("输入Y##spawn_y")) {
                XwzcMods::g_keyboard_target_buffer = g_spawn_y_buffer;
                XwzcMods::g_keyboard_target_buffer_size = sizeof(g_spawn_y_buffer);
                XwzcMods::g_keyboard_title = "输入出生Y坐标";
                XwzcMods::g_show_virtual_keyboard = true;
            }
            ImGui::SameLine();
            ImGui::Text("当前: %s", g_spawn_y_buffer);

            if (ImGui::Button("应用坐标设置")) {
                ApplySpawnPosition();
            }

            ImGui::Text("目标坐标: (%d, %d)", g_spawn_x, g_spawn_y);
            ImGui::TextColored(ImVec4(1,1,0,1), "注意: 请确保坐标在有效地图范围内!");
        }

        // 显示出生位置Hook状态
        ImGui::Text("PlayerController.Execute地址: %p", g_playerStatusUpdate_execute_address);
        if (g_playerStatusUpdate_execute_hooked) {
            ImGui::TextColored(ImVec4(0,1,0,1), "✅ Hook状态: 成功");
        } else {
            ImGui::TextColored(ImVec4(1,0,0,1), "❌ Hook状态: 失败");
        }

        // --- 网络日志 UI ---
        ImGui::Separator();
        ImGui::Text("--- 网络日志 ---");

        ImGui::Checkbox("启用网络日志", &g_network_log_enabled);
        ImGui::SameLine();
        if (ImGui::Button("打开日志窗口")) {
            g_show_network_log_window = true;
        }
        ImGui::SameLine();
        if (ImGui::Button("清空日志")) {
            g_network_log_messages.clear();
        }
        ImGui::SameLine();
        if (ImGui::Button("重新Hook")) {
            g_remoteIOHandle_outputString_hooked = false;
            InitNetworkLogHook();
        }

        ImGui::Text("已记录消息: %d/%d", (int)g_network_log_messages.size(), g_max_log_messages);

        // 显示Hook状态
        ImGui::Text("CommandController实例: %p", g_commandController_instance);
        ImGui::Text("IRemoteIOHandle实例: %p", g_remoteIOHandle_instance);
        ImGui::Text("OutputString地址: %p", g_remoteIOHandle_outputString_address);
        if (g_remoteIOHandle_outputString_hooked) {
            ImGui::TextColored(ImVec4(0,1,0,1), "✅ Hook状态: 成功");
        } else {
            ImGui::TextColored(ImVec4(1,0,0,1), "❌ Hook状态: 失败");
        }

        if (g_network_log_enabled && g_remoteIOHandle_outputString_hooked) {
            ImGui::TextColored(ImVec4(0,1,0,1), "网络日志已启用 - 正在记录发送数据");
        } else if (g_network_log_enabled && !g_remoteIOHandle_outputString_hooked) {
            ImGui::TextColored(ImVec4(1,1,0,1), "网络日志已启用 - 但Hook失败，无法记录数据");
        } else {
            ImGui::TextColored(ImVec4(1,0,0,1), "网络日志已禁用");
        }

        ImGui::Separator();
        ImGui::Text("--- 调试区 ---");
        if (ImGui::Button("手动初始化 Command Controller")) {
            InitCommandController();
        }
        if (ImGui::Button("测试移动 (向(0,0))")) {
            DoTestMove();
        }
        ImGui::Text("CommandController 实例: %p", g_commandController_instance);
        ImGui::Text("PlayerMove 方法地址: %p", CommandController_PlayerMove_ptr);

        // 绘制网络日志窗口
        DrawNetworkLogWindow();
    }

    // 绘制网络日志窗口
    void DrawNetworkLogWindow() {
        if (!g_show_network_log_window) return;

        ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
        if (ImGui::Begin("网络日志窗口", &g_show_network_log_window)) {
            // 工具栏
            if (ImGui::Button("清空日志")) {
                g_network_log_messages.clear();
            }
            ImGui::SameLine();
            if (ImGui::Button("复制全部")) {
                std::string allLogs;
                for (const auto& msg : g_network_log_messages) {
                    allLogs += msg + "\n";
                }
                ImGui::SetClipboardText(allLogs.c_str());
            }
            ImGui::SameLine();
            ImGui::Text("消息数量: %d", (int)g_network_log_messages.size());

            ImGui::Separator();

            // 日志内容区域
            ImGui::BeginChild("LogContent", ImVec2(0, 0), true);

            for (size_t i = 0; i < g_network_log_messages.size(); i++) {
                const std::string& msg = g_network_log_messages[i];

                // 根据消息类型设置颜色
                ImVec4 color = ImVec4(1, 1, 1, 1);  // 默认白色
                if (msg.find("SEND:") != std::string::npos) {
                    color = ImVec4(0, 1, 0, 1);  // 发送消息用绿色
                } else if (msg.find("RECV:") != std::string::npos) {
                    color = ImVec4(0, 0.8f, 1, 1);  // 接收消息用蓝色
                }

                ImGui::TextColored(color, "%s", msg.c_str());

                // 右键菜单
                if (ImGui::BeginPopupContextItem()) {
                    if (ImGui::MenuItem("复制此条消息")) {
                        ImGui::SetClipboardText(msg.c_str());
                    }
                    ImGui::EndPopup();
                }
            }

            // 自动滚动到底部
            if (ImGui::GetScrollY() >= ImGui::GetScrollMaxY()) {
                ImGui::SetScrollHereY(1.0f);
            }

            ImGui::EndChild();
        }
        ImGui::End();
    }

    // 更新CommandController实例 (仅用于显示信息)
    void UpdateCommandControllerInstance() {
        if (!g_commandControllerType || !Object_FindObjectsOfType) return;

        Array<void**>* commandControllers = Object_FindObjectsOfType(g_commandControllerType);
        if (commandControllers && commandControllers->getPointer() && commandControllers->getLength() > 0) {
            void* newInstance = commandControllers->getPointer()[0];
            if (newInstance != g_commandController_instance) {
                g_commandController_instance = newInstance;
                LOGI("✅ 找到CommandController实例: %p", g_commandController_instance);

                // 获取m_sender字段 (IRemoteIOHandle实例) - 仅用于显示
                if (g_commandController_instance) {
                    // CommandController的m_sender字段偏移为32 (根据之前的分析)
                    g_remoteIOHandle_instance = *(void**)((uintptr_t)g_commandController_instance + 32);
                    LOGI("✅ 获取到IRemoteIOHandle实例: %p", g_remoteIOHandle_instance);
                }
            }
        } else {
            if (g_commandController_instance) {
                LOGI("❌ CommandController实例丢失");
                g_commandController_instance = nullptr;
                g_remoteIOHandle_instance = nullptr;
            }
        }
    }

    // 初始化网络日志Hook
    void InitNetworkLogHook() {
        // 如果已经Hook过了，跳过重复Hook
        if (g_remoteIOHandle_outputString_hooked) {
            LOGI("⚠️ 已经Hook过，跳过重复Hook");
            return;
        }

        // 直接使用Il2CppGetMethodOffset获取RemoteIOController.IOHandle.OutputString的地址
        g_remoteIOHandle_outputString_address = Il2CppGetMethodOffset(
            "Assembly-CSharp.dll", "", "RemoteIOController+IOHandle", "OutputString", 1);

        if (!g_remoteIOHandle_outputString_address) {
            LOGE("❌ 无法获取RemoteIOController.IOHandle.OutputString方法地址");
            return;
        }

        LOGI("✅ 获取到RemoteIOController.IOHandle.OutputString方法地址: %p", g_remoteIOHandle_outputString_address);

        // Hook OutputString方法
        int hookResult = DobbyHook(g_remoteIOHandle_outputString_address,
                                   (void*)IRemoteIOHandle_OutputString_Hook,
                                   (void**)&IRemoteIOHandle_OutputString_o);
        if (hookResult == 0) {
            LOGI("✅ 成功Hook RemoteIOController.IOHandle.OutputString");
            g_remoteIOHandle_outputString_hooked = true;
        } else {
            LOGE("❌ Hook RemoteIOController.IOHandle.OutputString 失败, 错误码: %d", hookResult);
            g_remoteIOHandle_outputString_hooked = false;
        }
    }

    // 主循环，由后台线程调用
    void Loop() {
        if (!g_cameraType) return;

        g_allCameras = Object_FindObjectsOfType(g_cameraType);

        if (!g_initial_size_set && g_allCameras && g_allCameras->getLength() > 1) {
            void* secondCamera = g_allCameras->getPointer()[1];
            if (secondCamera) {
                g_ortho_size = get_orthographicSize(secondCamera);
                g_initial_size_set = true;
            }
        }
        
        // --- Speed Hack: Find Local Player ---
        if (g_playerControllerType && Object_FindObjectsOfType && get_isSelfPlayer_ptr) {
            Array<void**>* all_players_raw = Object_FindObjectsOfType(g_playerControllerType);
            if (all_players_raw && all_players_raw->getPointer() && all_players_raw->getLength() > 0) {
                bool found_player = false;
                for (int i = 0; i < all_players_raw->getLength(); ++i) {
                    void* player = all_players_raw->getPointer()[i];
                    if (player && get_isSelfPlayer_ptr(player)) {
                        g_local_player_instance = player;
                        found_player = true;
                        break;
                    }
                }
                if (!found_player) {
                    g_local_player_instance = nullptr;
                }
            } else {
                 g_local_player_instance = nullptr;
            }
        }

        // 更新CommandController实例和网络日志Hook
        UpdateCommandControllerInstance();
    }


    // 总初始化，在主初始化之后调用
    void Init() {
        Object_FindObjectsOfType = (Array<void**>*(*)(void*)) (uintptr_t) Il2CppGetMethodOffset("UnityEngine.dll", "UnityEngine", "Object", "FindObjectsOfType", 1);
        Type_GetTypeName = (GetTypeFunc_t)Il2CppGetMethodOffset("mscorlib.dll", "System", "Type", "GetType", 1);
        get_orthographicSize = (get_orthographicSize_t)Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_orthographicSize", 0);
        set_orthographicSize = (set_orthographicSize_t)Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "set_orthographicSize", 1);

        if(Type_GetTypeName) {
            g_cameraType = Type_GetTypeName(Il2CppString::Create("UnityEngine.Camera, UnityEngine.CoreModule"));
        }

        // 新增 Speed Hack 相关初始化
        set_speed_ptr = (void (*)(void*, float))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "set_speed", 1);
        get_isSelfPlayer_ptr = (bool (*)(void*))Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "get_isSelfPlayer", 0);
        void* update_method = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "Update", 0);

        // 初始化出生位置修改Hook
        g_playerStatusUpdate_execute_address = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "Execute", 1);
        if (g_playerStatusUpdate_execute_address) {
            LOGI("✅ 成功获取 PlayerController.Execute 地址: %p", g_playerStatusUpdate_execute_address);
            int hookResult = DobbyHook(g_playerStatusUpdate_execute_address, (void*)PlayerStatusUpdate_Execute_Hook, (void**)&PlayerStatusUpdate_Execute_o);
            if (hookResult == 0) {
                LOGI("✅ 成功Hook PlayerController.Execute");
                g_playerStatusUpdate_execute_hooked = true;
            } else {
                LOGE("❌ Hook PlayerController.Execute 失败, 错误码: %d", hookResult);
                g_playerStatusUpdate_execute_hooked = false;
            }
        } else {
            LOGE("❌ 获取 PlayerController.Execute 地址失败");
            g_playerStatusUpdate_execute_hooked = false;
        }

        // 初始化CommandController类型
        if (Type_GetTypeName) {
            g_commandControllerType = Type_GetTypeName(Il2CppString::Create("CommandController, Assembly-CSharp"));
            if (g_commandControllerType) {
                LOGI("✅ 成功获取CommandController类型: %p", g_commandControllerType);
            } else {
                LOGE("❌ 获取CommandController类型失败");
            }
        }

        // 初始化网络日志Hook
        InitNetworkLogHook();

        // 只初始化类型，不获取实例和方法
        if (Type_GetTypeName) {
            g_playerControllerType = Type_GetTypeName(Il2CppString::Create("PlayerController, Assembly-CSharp"));
            // g_commandControllerType = Type_GetTypeName(Il2CppString::Create("CommandController, Assembly-CSharp"));
        }

        if (update_method) {
            DobbyHook(update_method, (void*)PlayerController_Update_Hook, (void**)&PlayerController_Update_o);
        }
    }

    void OnUpdate() {
        // 主线程任务处理入口
    }
}