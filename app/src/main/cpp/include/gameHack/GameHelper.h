//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "Il2Cpp.h"
#include "UnityResolve.hpp"
#include <vector>
#include <string>

namespace GameHelper {
    // 获取GameServerManager静态实例
    void* getGameServerManager() {
        void* instance = nullptr;
        Il2CppGetStaticFieldValue("Assembly-CSharp.dll", "", "GameServerManager", "s_instance", &instance);
        if (instance != nullptr) {
            return instance;
        }
        return nullptr;
    }

    // 动态获取函数地址
    void* GetMethodAddress(const char* assemblyName, const char* namespaze, const char* klassName, const char* methodName, const std::vector<std::string>& arg_types = {}) {
        auto assembly = UnityResolve::Get(assemblyName);
        if (!assembly) return nullptr;
        auto klass = assembly->Get(klassName, namespaze);
        if (!klass) return nullptr;
        auto method = klass->Get<UnityResolve::Method>(methodName, arg_types);
        if (!method) return nullptr;
        return method->function;
    }

    // 获取字段偏移
    int32_t GetFieldOffset(const char* assemblyName, const char* namespaze, const char* klassName, const char* fieldName) {
        auto assembly = UnityResolve::Get(assemblyName);
        if (!assembly) return -1;
        auto klass = assembly->Get(klassName, namespaze);
        if (!klass) return -1;
        auto field = klass->Get<UnityResolve::Field>(fieldName);
        if (!field) return -1;
        return field->offset;
    }
} 