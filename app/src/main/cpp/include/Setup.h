#pragma once

#include "log.h"
#include "hua.h"
#include "global.h"
#include "OPPOSans-H.h"
#include "Obfuscate.h"
#include "GLES3/gl3.h"

FUNC(void, glViewport, int a, int b, int c, int d) {
    if (width < c && height < d) {
        width = c;
        height = d;
    }
    orig_glViewport(a, b, c, d);
    return;
}

//FUNC(void*, touch, void *thiz, void *p0, bool p1, long p2, unsigned int *p3, void **p4) {
//    auto ret = orig_touch(thiz, p0, p1, p2, p3, p4);
//    if (g_Initialized)
//        if (*p4 != nullptr)
//            ImGui_ImplAndroid_HandleInputEvent((AInputEvent * ) * p4);
//    return ret;
//}

FUNC(void, touch, void *thiz, void *ex_ab, void *ex_ac) {
    orig_touch(thiz, ex_ab, ex_ac);
    if (g_Initialized)
        ImGui_ImplAndroid_HandleInputEvent((AInputEvent *) thiz);
    return;
}

FUNC(EGLBoolean, swapBuffers, EGLDisplay dpy, EGLSurface surface) {
    if (!g_Initialized) {
        ImGui::CreateContext();
        ImGui::SetCurrentContext(ImGui::GetCurrentContext());
        ImGuiStyle *style = &ImGui::GetStyle();
        ImGui::StyleColorsClassic();
        style->WindowPadding = ImVec2(4, 4);
        style->WindowRounding = 4.0f;
        style->WindowBorderSize = 0.0f;
        style->FramePadding = ImVec2(5, 5);
        style->FrameRounding = 4.0f;
        style->ItemInnerSpacing = ImVec2(4, 4);
        style->IndentSpacing = 5.0f;
        style->ScrollbarSize = 16.0f;
        style->ScrollbarRounding = 8.0f;
        style->GrabMinSize = 4.0f;
        style->GrabRounding = 4.0f;
        style->ScaleAllSizes(2);
        ImGuiIO *io = &ImGui::GetIO();
        data_path.append("/imgui.ini");
        io->IniFilename = data_path.c_str();
        ImGui_ImplOpenGL3_Init("#version 300 es");
        ImFontConfig font_cfg;
        font_cfg.FontDataOwnedByAtlas = false;
        io->Fonts->AddFontFromMemoryTTF((void *) OPPOSans_H, OPPOSans_H_size, 50.0f, &font_cfg,
                                        io->Fonts->GetGlyphRangesChineseFull());
        g_Initialized = true;
        LOGD("ImGui已加载");
    }

    static auto count = 0;
    ImGuiIO *io = &ImGui::GetIO();
    count++;
    if (count == 1) {
        io->DisplaySize = ImVec2(width, height);
        orig_glViewport(0, 0, width, height);
    } else if (count > 300) count = 0;

    ImGui_ImplOpenGL3_NewFrame();
    ImGui::NewFrame();

    try {
        _main();
    } catch (void *) {}

    ImGui::EndFrame();
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    return orig_swapBuffers(dpy, surface);
}



void (*old_MotionEvent)(void* _this, void* a1, void* a2, void* a3, void* a4, void* a5, void* a6, void* a7, void* a8, void* a9, void* a10, void* a11, void* a12, void* a13, void* a14, void* a15, void* a16, void* a17, void* a18, void* a19, void* a20, void* a21, void* a22, void* a23, void* a24);
void hook_MotionEvent(void* _this, void* a1, void* a2, void* a3, void* a4, void* a5, void* a6, void* a7, void* a8, void* a9, void* a10, void* a11, void* a12, void* a13, void* a14, void* a15, void* a16, void* a17, void* a18, void* a19, void* a20, void* a21, void* a22, void* a23, void* a24) {
    old_MotionEvent(_this, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15, a16, a17, a18, a19, a20, a21, a22, a23, a24);
    if (g_Initialized) {
        ImGui_ImplAndroid_HandleInputEvent((AInputEvent *) _this);
    }
    return;
}

void (*old_Input)(void *thiz, void *ex_ab, void *ex_ac);
void hook_Input(void *thiz, void *ex_ab, void *ex_ac) {
    old_Input(thiz, ex_ab, ex_ac);
    if (g_Initialized) {
        ImGui_ImplAndroid_HandleInputEvent((AInputEvent *) thiz);
    }
    return;
}

void initImGui() {
    auto egl = get_module("EGL");
    auto glesv2 = get_module("GLESv2");
    auto glesv3 = get_module("GLESv3");
    auto input = get_module("input");


    LOGD("EGL: 0x%lX", (uintptr_t) egl);
    LOGD("GLESv2: 0x%lX", (uintptr_t) glesv2);
    LOGD("GLESv3: 0x%lX", (uintptr_t) glesv3);
    LOGD("Input: 0x%lX", (uintptr_t) input);
    HOOK(xdl_sym(egl, OBFUSCATE("eglSwapBuffers"), nullptr), swapBuffers);
//    HOOK(xdl_sym(input,
//                 OBFUSCATE(
//                         "_ZN7android13InputConsumer21initializeMotionEventEPNS_11MotionEventEPKNS_12InputMessageE"),
//                 nullptr), touch);
    void *sym_input = DobbySymbolResolver(OBFUSCATE("/system/lib/libinput.so"), OBFUSCATE("_ZN7android13InputConsumer21initializeMotionEventEPNS_11MotionEventEPKNS_12InputMessageE"));
    if (sym_input != nullptr) {
        DobbyHook((void *)sym_input, (void *) hook_Input, (void **)&old_Input);
    } else {
        void *InputMessage = DobbySymbolResolver(OBFUSCATE("/system/lib/libinput.so"), OBFUSCATE("_ZN7android11MotionEvent10initializeEiijiNSt3__15arrayIhLm32EEEiiiiiiNS_20MotionClassificationERKNS_2ui9TransformEffffS8_llmPKNS_17PointerPropertiesEPKNS_13PointerCoordsE"));
        if (InputMessage != nullptr) {
            DobbyHook((void *)InputMessage, (void *) hook_MotionEvent, (void **)&old_MotionEvent);
        }else{
            void *_InputMessage = DobbySymbolResolver(OBFUSCATE("/system/lib/libinput.so"), OBFUSCATE("_ZN7android11MotionEvent10initializeEiijNS_2ui16LogicalDisplayIdENSt3__15arrayIhLm32EEEiiiiiiNS_20MotionClassificationERKNS1_9TransformEffffS9_llmPKNS_17PointerPropertiesEPKNS_13PointerCoordsE"));
            if (_InputMessage != nullptr) {
                DobbyHook((void *)_InputMessage, (void *) hook_MotionEvent, (void **)&old_MotionEvent);
            }
        }
    }
    HOOK(xdl_sym(glesv2, OBFUSCATE("glViewport"), nullptr), glViewport);
    xdl_close(egl);
    xdl_close(glesv2);
    xdl_close(glesv3);
    xdl_close(input);
}
